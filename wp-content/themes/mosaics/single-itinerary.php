<?php

/**

 * Itinerary detail page

 *

 * @package Theia

 */



$post_id = get_the_id();



// >> Mosaic info

$intro    = get_post_meta_direct( $post_id, '_itinerary_intro', true );

$mosaics  = get_post_meta_direct( $post_id, '_itinerary_markers', true );

// Verificar que $mosaics és un array vàlid
if ( !is_array( $mosaics ) ) {
	$mosaics = array();
}



$download = get_post_meta_direct( $post_id, '_itinerary_file', true );

if (is_array($download)) $download = array_keys($download);



// Image

$img = get_the_post_thumbnail_url($post_id, 'mosaic-list' );

$mosaicImg = $img ? $img : 'https://placehold.it/740x420';



get_header(); ?>

<style>
.itinerary-transport-controls {
	margin-bottom: 20px;
	padding: 15px;
	background: #f8f9fa;
	border-radius: 8px;
	border: 1px solid #e9ecef;
}

.itinerary-transport-controls h4 {
	margin: 0 0 10px 0;
	font-size: 16px;
	color: #333;
	font-weight: 600;
}

.transport-options {
	display: flex;
	gap: 15px;
	flex-wrap: wrap;
}

.transport-option {
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 10px 15px;
	background: white;
	border: 2px solid #dee2e6;
	border-radius: 6px;
	cursor: pointer;
	transition: all 0.2s ease;
	min-width: 120px;
}

.transport-option:hover {
	border-color: #ff6600;
	background: #fff5f0;
}

.transport-option input[type="radio"] {
	margin: 0;
}

.transport-option input[type="radio"]:checked + .transport-icon + span {
	font-weight: 600;
	color: #ff6600;
}

.transport-option input[type="radio"]:checked {
	accent-color: #ff6600;
}

.transport-option:has(input[type="radio"]:checked) {
	border-color: #ff6600;
	background: #fff5f0;
}

.transport-icon {
	font-size: 18px;
}

@media (max-width: 768px) {
	.transport-options {
		flex-direction: column;
	}

	.transport-option {
		min-width: auto;
		justify-content: flex-start;
	}
}
</style>

<main id="main" class="site-main site-main--itinerary" role="main">



<?php if ( have_posts() ) : ?>

	<?php while ( have_posts() ) : the_post(); ?>

	<section class="itinerary-single">

		<div class="wrapper">



			<header class="itinerary-single__header">

				<h3 class="itinerary-single__section"><?php _e( 'Itineraris', 'frontend' ); ?></h3>

				<h2 class="itinerary-single__title"><?php the_title(); ?></h2>

			</header>



			<div class="itinerary-single__group">



				<div class="itinerary-single__item">

					<div class="itinerary-single__description">

						<p class="itinerary-single__intro"><?php echo $intro ?></p>

						<div class="cms__content">

							<?php the_content() ?>

						</div>

					</div>



					<div class="itinerary-single__duplicated-content" aria-hidden="true">

						<?php if ($mosaicImg): ?>

							<div class="itinerary-single__image">

								<img src="<?php echo $mosaicImg ?>" alt="<?php echo single_post_title(); ?>" class="itinerary-single__image-item">

							</div>

						<?php endif ?>



						<?php if( is_array($download) && count($download) > 0 && $download[0] > 0 ): ?>

							<div class="itinerary-single__download">

								<a href="<?php echo wp_get_attachment_url($download[0]); ?>" title="<?php _e( 'Descarregar itinerari', 'frontend' ); ?>">

									<span><?php _e( 'Descarregar itinerari', 'frontend' ); ?></span>

									<span><?php the_title(); ?></span>

								</a>

							</div>

						<?php endif ?>



						<!-- Controls de transport -->
						<div class="itinerary-transport-controls">
							<h4><?php _e('Tipus de transport:', 'frontend'); ?></h4>
							<div class="transport-options">
								<label class="transport-option">
									<input type="radio" name="transport" value="foot-walking" checked>
									<span class="transport-icon">🚶‍♂️</span>
									<span><?php _e('A peu', 'frontend'); ?></span>
								</label>
								<label class="transport-option">
									<input type="radio" name="transport" value="driving-car">
									<span class="transport-icon">🚗</span>
									<span><?php _e('Amb cotxe', 'frontend'); ?></span>
								</label>
								<label class="transport-option">
									<input type="radio" name="transport" value="cycling-regular">
									<span class="transport-icon">🚴‍♂️</span>
									<span><?php _e('Amb bici', 'frontend'); ?></span>
								</label>
							</div>
						</div>



						<?php if ( defined( 'WP_DEBUG' ) && WP_DEBUG ): ?>
							<!-- Debug: Itinerary ID = <?php echo $post_id ?>, Mosaics count = <?php echo count($mosaics) ?> -->
							<script>
							console.log('🗺️ Itinerary Debug Info:');
							console.log('- Itinerary ID: <?php echo $post_id ?>');
							console.log('- Mosaics count: <?php echo count($mosaics) ?>');

							console.log('- Elements with .js__itinerary__desktop:', document.querySelectorAll('.js__itinerary__desktop').length);
							console.log('- Maplace available:', typeof Maplace !== 'undefined');
							console.log('- Google Maps available:', typeof google !== 'undefined' && typeof google.maps !== 'undefined');
							</script>
						<?php endif ?>

					</div>



					<?php if ( !empty($mosaics) && is_array($mosaics) && count($mosaics) > 0 && $mosaics[0] > 0 ): ?>

					<div class="itinerary-single__route">

						<h3 class="itinerary-single__route-title"><?php _e( 'Itinerari', 'frontend' ); ?></h3>



						<ol class="itinerary-single__route-steps">

							<?php

								$stepIndex = 1;

								foreach ($mosaics as $mosaic_id):

									$itnr_title = get_post_meta_direct($mosaic_id, '_mosaic_itnr_title', true);

									$itnr_text = get_post_meta_direct($mosaic_id, '_mosaic_itnr_text', true);

									$itnr_title = $itnr_title ? $itnr_title : get_the_title($mosaic_id);



									$itnr_image = get_post_meta_direct($mosaic_id, '_mosaic_itnr_image', true);

									// Verificar que $itnr_image és un array vàlid abans d'accedir
									$img = '';
									if ( is_array( $itnr_image ) && isset( $itnr_image[0] ) && $itnr_image[0] > 0 ) {
										$img = wp_get_attachment_image_url($itnr_image[0], 'mosaic-list');
									}

									$location = get_post_meta_direct( $mosaic_id, '_geoposition', true );



									// Only if have geolocation

									if ( is_array( $location ) &&
										 isset( $location['lat'] ) && isset( $location['lng'] ) &&
										 $location['lat'] > 0 && $location['lng'] > 0 ) {

							?>

								<li class="itinerary-single__route-step">

									<img src="<?php echo $img ?>" alt="" class="itinerary-single__route-steps-image">

									<div class="itinerary-single__route-steps-details">

										<h4 class="itinerary-single__route-steps-title"><a href="<?php echo get_permalink($mosaic_id); ?>" title="<?php echo $itnr_title ?>"><?php echo $stepIndex . '.- <span>' . $itnr_title . '</span>'; ?></a></h4>

										<p class="itinerary-single__route-steps-description"><?php echo $itnr_text; ?></p>

									</div>

								</li>

							<?php

							$stepIndex++;

							}

							endforeach

							?>

						</ol>

					</div>

					<?php endif ?>

				</div>



				<div class="itinerary-single__item">

					<?php if ($mosaicImg): ?>

						<div class="itinerary-single__image">

							<img src="<?php echo $mosaicImg ?>" alt="<?php echo single_post_title(); ?>" class="itinerary-single__image-item">

						</div>

					<?php endif ?>



					<?php if( is_array($download) && count($download) > 0 && $download[0] > 0 ): ?>

						<div class="itinerary-single__download">

							<a href="<?php echo wp_get_attachment_url($download[0]); ?>" title="<?php _e( 'Descarregar itinerari', 'frontend' ); ?>">

								<span><?php _e( 'Descarregar itinerari', 'frontend' ); ?></span>

								<span><?php the_title(); ?></span>

							</a>

						</div>

					<?php endif ?>



					<!-- Controls de transport desktop -->
					<div class="itinerary-transport-controls">
						<h4><?php _e('Tipus de transport:', 'frontend'); ?></h4>
						<div class="transport-options">
							<label class="transport-option">
								<input type="radio" name="transport-desktop" value="foot-walking" checked>
								<span class="transport-icon">🚶‍♂️</span>
								<span><?php _e('A peu', 'frontend'); ?></span>
							</label>
							<label class="transport-option">
								<input type="radio" name="transport-desktop" value="driving-car">
								<span class="transport-icon">🚗</span>
								<span><?php _e('Amb cotxe', 'frontend'); ?></span>
							</label>
							<label class="transport-option">
								<input type="radio" name="transport-desktop" value="cycling-regular">
								<span class="transport-icon">🚴‍♂️</span>
								<span><?php _e('Amb bici', 'frontend'); ?></span>
							</label>
						</div>
					</div>

					<!-- Element mòbil per al mapa -->
					<div class="js__itinerary itinerary-single__map-mobile" data-id="<?php echo $post_id ?>" style="display: none;"></div>

					<!-- Element desktop per al mapa -->
					<div class="js__itinerary__desktop itinerary-single__map" data-id="<?php echo $post_id ?>"></div>

					<script>
					// Debug sempre actiu per verificar funcionament
					console.log('🗺️ ITINERARY DEBUG - SEMPRE ACTIU:');
					console.log('- Itinerary ID:', <?php echo $post_id ?>);
					console.log('- Mosaics count:', <?php echo count($mosaics) ?>);
					console.log('- Body classes:', document.body.className);
					console.log('- Elements .js__itinerary:', document.querySelectorAll('.js__itinerary').length);
					console.log('- Elements .js__itinerary__desktop:', document.querySelectorAll('.js__itinerary__desktop').length);
					console.log('- Maplace available:', typeof Maplace !== 'undefined');
					console.log('- jQuery available:', typeof jQuery !== 'undefined');
					</script>

				</div>



			</div>













		</div>

	</section>

	<?php endwhile; ?>

<?php endif; ?>



</main>

<?php

get_footer();


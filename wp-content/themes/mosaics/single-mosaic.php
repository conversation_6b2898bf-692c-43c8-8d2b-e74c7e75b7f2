<?php
/**
 * Mosaic detalle
 *
 * @package Theia
 */

$post_id = get_the_id();

// >> Mosaic info
$location         = get_post_meta_direct( $post_id, '_geoposition', true );
$marker           = get_post($post_id);
$title            = $marker->post_title;
$mosaic_district  = theia__get_first_term( $post_id, 'mosaic_district');
$mosaic_technique = theia__get_first_term( $post_id, 'mosaic_technique');
$mosaic_period    = theia__get_first_term( $post_id, 'mosaic_period');
$mosaic_address   = get_post_meta_direct( $post_id, '_mosaic_address', true );

// Image
$img = wp_get_attachment_image_url( get_post_thumbnail_id($post_id), 'full' );
$alt = get_post_meta_direct( get_post_thumbnail_id($post_id), '_wp_attachment_image_alt', true );
$img = $img ? $img : 'https://placehold.it/740x420';

$count_uploads = mosaic__get_mosaic_uploads_count($post_id);
$count_images = mosaic__get_mosaic_images_count($post_id);

// Funció segura per mostrar noms
function safe_term_name($term, $default = '') {
    return $term && is_object($term) ? $term->name : $default;
}

get_header(); ?>
<main id="main" class="site-main site-main--mosaic" role="main">

<?php if ( have_posts() ) : ?>

	<article class="mosaic">

	<?php while ( have_posts() ) : the_post(); ?>
	<header class="mosaic__summary">
		<div class="wrapper">
            <div class="details">
			<div class="mosaic__summary-item mosaic__summary-item--back">
				<a href="javascript:history.back()"><?php _e('Tornar','frontend'); ?></a>
			</div>

			<ul class="mosaic__summary-item mosaic__summary-item--details mosaic__details">
				<li class="mosaic__detail mosaic__detail--district"><?php the_title() ?></li>
				<li class="mosaic__detail"><?php echo safe_term_name($mosaic_district, __('Sense districte', 'frontend')); ?></li>
				<li class="mosaic__detail"><?php echo safe_term_name($mosaic_technique, __('Sense tècnica', 'frontend')); ?></li>
				<li class="mosaic__detail"><?php echo safe_term_name($mosaic_period, __('Sense període', 'frontend')); ?></li>
			</ul>

			<div class="mosaic__summary-item  mosaic__summary-item--stats mosaic__stats">
				<span class="mosaic__stats-item--images"><?php echo $count_images ?></span> <?php echo ($count_images == 1) ? __( 'imatge', 'frontend' ) : __( 'imatges', 'frontend' ); ?> | <span class="mosaic__stats-item--participants"><?php echo $count_uploads ?></span> <?php echo ($count_uploads == 1) ? __( 'participant', 'frontend' ) : __( 'participants', 'frontend' ); ?>
			</div>
</div>
			<div class="mosaic__summary-item mosaic__summary-item--image" tabindex="0">
				<span><img src="<?php echo $img ?>" alt="<?php echo $alt; ?>" class="js__gallery-btn">
				<p class="autor-imatge"><?php echo $alt; ?></p>
					</span>
			</div>

			<div class="mosaic__summary-item mosaic__summary-item--map">
				<?php if ( isset($location['lat']) && isset($location['lng']) && $location['lat'] != '' && $location['lng'] != '' ): ?>
					<div class="js__mosaic__single mosaic__map" data-lat="<?php echo $location['lat'] ?>" data-lon="<?php echo $location['lng'] ?>"></div>
				<?php endif ?>
			</div>

		</div>
	</header>

	<div class="mosaic__description">
		<div class="wrapper">
			<div class="cms__content">
				<?php the_content() ?>
			</div>
		</div>
	</div>
	<?php endwhile; ?>

<?php
// > GALLERY LOOP
$gallery_args = array(
	'post_type'      => 'mosaic_upload',
	'orderby'        => 'menu_order date',
	'post_status'    => 'published',
	'nopaging'       => true,
	'meta_query'     => array(
		array(
			'key'     => '_mosaic_upload_linked',
			'value'   => $post_id,
			'compare'=> '='
		)
	)
);
$gallery = new WP_Query( $gallery_args );

if ( $gallery->have_posts() ):
?>

	<div class="mosaic__gallery">
		<div class="wrapper">
			<h3 class="mosaic__gallery-title"><?php _e('El mosaic en imatges','frontend'); ?></h3>
			<ul class="mosaic__gallery-list grid">
				<li class="gutter-sizer"></li>
				<?php while ( $gallery->have_posts() ) : $gallery->the_post();
					get_template_part('parts/mosaic','gallery');
				endwhile; ?>
			</ul>
		</div>
	</div>
<?php endif; ?>


<?php
// > RELATED LOOP
$related_args = array(
	'post_type'      => 'mosaic',
	'orderby'        => 'rand',
	'posts_per_page' => 4,
	'tax_query'      => array(
	    array(
			'taxonomy' => 'mosaic_technique',
			'field'    => 'term_id',
			'terms'    => $mosaic_technique ? $mosaic_technique->term_id : 0
		),
		array(
			'taxonomy' => 'mosaic_district',
			'field'    => 'term_id',
			'terms'    => $mosaic_district ? $mosaic_district->term_id : 0
		)
	)
);

$related = new WP_Query( $related_args );
$number_of_related_in_tecnic_district = count($related->posts);
$number_of_extra = 0;

if ($number_of_related_in_tecnic_district < 4){
    $number_of_extra = 4 - $number_of_related_in_tecnic_district;
    $related_tecnica = array(
        'post_type'      => 'mosaic',
        'orderby'        => 'rand',
        'posts_per_page' => $number_of_extra,
        'tax_query'      => array(
            array(
                'taxonomy' => 'mosaic_technique',
                'field'    => 'term_id',
                'terms'    => $mosaic_technique ? $mosaic_technique->term_id : 0
            )
        )
    );
    $related_t = new WP_Query( $related_tecnica );
}

if ( $related->have_posts() ): ?>

	<aside class="mosaic__related">
		<div class="wrapper">
			<h4 class="mosaic__related-title"><?php echo __('Altres mosaics similars','frontend')?></h4>
			<ul class="inventory__list inventory__list--related js__related-slider">
				<?php
					while ( $related->have_posts() ) : $related->the_post();
						get_template_part('parts/item','mosaic');
					endwhile;
					if($number_of_extra>0){
                        while ( $related_t->have_posts() ) : $related_t->the_post();
                            get_template_part('parts/item','mosaic');
                        endwhile;
                    }
				?>
			</ul>
		</div>
	</aside>

<?php endif; ?>

	</article>

<?php endif; ?>

</main>
<?php get_footer(); ?>
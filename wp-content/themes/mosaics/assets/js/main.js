var SN = {};
SN.cache = {
    init: function () {
        SN.cache.$window = $(window), SN.cache.$html = $("html"), SN.cache.$body = SN.cache.$html.find("body")
    }
}, SN.cookies = {
    gaID: null,
    gaCode: null,
    gaContainer: null,
    lopd: !1,
    init: function () {
        SN.cookies.gaID = "UA-XXXXXXX-XX", SN.cookies.gaYourSite = "yoursite.com", SN.cookies.gaCode = "<script>(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o), m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)})(window,document,'script','//www.google-analytics.com/analytics.js','ga');ga('create', '" + SN.cookies.gaID + "' , '" + SN.cookies.gaYourSite + "');ga('send', 'pageview');<\/script>", SN.cookies.gaContainer = $("#cookie_alert"), $.cookie("lopd") ? SN.cookies.accepted() : SN.cookies.gaContainer.addClass("shown").find("button").on("click", function () {
            SN.cookies.lopd || SN.cookies.accepted()
        })
    },
    accepted: function () {
        $.cookie("lopd", "true"), SN.cookies.lopd = !0, SN.cookies.gaContainer.removeClass("shown").html(SN.cookies.gaCode)
    }
}, SN.externalLinks = {
    init: function () {
        SN.cache.$body.find('a.ext,a[rel="external"]').each(function (e, i) {
            $(this).attr("target", "_blank"), $(this).attr("rel") || $(this).attr("rel", "external")
        })
    }
}, SN.sliders = {
    init: function () {
        var a = SN.cache.$body,
            e = SN.cache.$body.find(".js__home-slider"),
            i = SN.cache.$body.find(".js__related-slider"),
            o = SN.cache.$body.find(".js__gallery-item"),
            s = "js__lightbox--gallery",
            n = SN.cache.$body.find(".js__page-slider");
        e.length && e.each(function (e, i) {
            1 < $(this).find(".js__home-slide-item").length && ($(this).addClass("js__home-slider--active"), $(this).slick({
                autoplay: !0,
                autoplaySpeed: 5e3,
                speed: 2e3
            }))
        });
        var t = function () {
            i.hasClass("js__related-slider--active") || 1 < i.find(".inventory__item").length && i.slick({
                autoplay: !0,
                autoplaySpeed: 5e3,
                speed: 2e3
            }).addClass("js__related-slider--active")
        };
        $(window).width() < 1024 && t(), $(window).bind("enterBreakpoint320", function () {
            t()
        }), $(window).bind("enterBreakpoint768", function () {
            t()
        }), $(window).bind("enterBreakpoint1024", function () {
            i.hasClass("js__related-slider--active") && i.slick("unslick").removeClass("js__related-slider--active")
        });
        o.length && o.find(".js__gallery-btn").on("click", function (e) {
            e.preventDefault();
            var n, i = $(this).parents(".js__gallery-item"),
                t = o.index(i);
            a.find(".js__lightbox--gallery").length < 1 && (n = "", n = '<div class="lightbox lightbox--gallery ' + s + '">', n += '<div class="wrapper">', n += '<div class="gallery-slider">', n += '<button class="gallery-slider__close-btn js__gallery-close-btn">Cerrar</button>', n += '<ul class="gallery-slider__list js__gallery-slider">', o.each(function (e, i) {
                n += '<li class="gallery-slider__item">', n += '<div class="gallery-slider__cover">', n += '<img src="' + $(this).find(".js__gallery-image").data("full-image") + '">', n += "</div>", n += '<div class="gallery-slider__meta">' + $(this).find(".js__gallery-item-meta").text() + "</div>", n += "</li>"
            }), n += "</ul>", n += "</div>", n += "</div>", n += "</div>", a.append(n), a.find(".js__gallery-close-btn").on("click", function (e) {
                e.preventDefault(), a.find(".js__gallery-slider").slick("unslick"), $("." + s).addClass("lightbox--is-visible").remove()
            })), $("." + s).addClass("lightbox--is-visible"), a.find(".js__gallery-slider").slick({
                autoplay: !0,
                autoplaySpeed: 5e3,
                speed: 2e3
            }), a.find(".js__gallery-slider").slick("slickGoTo", t)
        }), n.length && 1 < n.find(".js__page-slider-item").length && n.slick({
            autoplay: !0,
            autoplaySpeed: 5e3,
            speed: 2e3
        })
    }
}, SN.mobileMenu = {
    init: function () {
        var e = SN.cache.$body.find(".site-header__navigation"),
            i = e.find(".menu-toggle"),
            n = e.find(".site-header__navigation-container"),
            t = e.find(".menu > .menu-item-has-children > a"),
            a = function () {
                t.removeClass("menu-item--is-open"), n.find(".sub-menu").removeAttr("style")
            };
        e.length && (i.on("click", function (e) {
            e.preventDefault(), $(this).toggleClass("menu-toggle--is-open"), $(this).hasClass("menu-toggle--is-open") ? n.addClass("site-header__navigation-container--visible").slideDown() : (a(), n.removeClass("site-header__navigation-container--visible").slideUp())
        }), t.on("click", function (e) {
            $(window).width() < 1024 && (e.preventDefault(), $(this).toggleClass("menu-item--is-open"), $(this).hasClass("menu-item--is-open") ? $(this).siblings(".sub-menu").slideDown() : $(this).siblings(".sub-menu").slideUp())
        }), $(window).resize(function (e) {
            1024 <= $(window).width() && n.hasClass("site-header__navigation-container--visible") && (a(), n.removeAttr("style").removeClass("site-header__navigation-container--visible"), i.removeClass("menu-toggle--is-open"))
        }))
    }
}, SN.map = {
    canvas: null,
    loading: null,
    counter: null,
    mapInstance: null, // Guardar instància del mapa
    markerClusterer: null, // Instància del MarkerClusterer
    filterCountsTimeout: null, // Per debounce de filter counts
    singleCanvas: null,
    itineraryMap: null,
    allMarkers: [], // Guardar tots els markers per clustering dinàmic
    clusterMarkers: [], // Guardar markers de clusters creats
    zoomTimeout: null, // Per debounce de zoom events
    ajaxCall: {
        action: "theia__get_map_markers",
        security: theia.ajaxNonce
    },
    init: function () {
        SN.map.canvas = SN.cache.$body.find(".js__map"), SN.map.loading = SN.cache.$body.find(".js__map__loading"), SN.map.counter = SN.cache.$body.find(".js__filter__counter"), SN.map.singleCanvas = SN.cache.$body.find(".js__mosaic__single"), SN.map.itineraryMap = SN.cache.$body.find(".js__itinerary");
        var e = SN.cache.$body.find(".js__filters"),
            i = SN.cache.$body.find(".js__filter__district"),
            n = SN.cache.$body.find(".js__filter__technique"),
            t = SN.cache.$body.find(".js__filter__period"),
            a = (SN.cache.$body.find(".js__filter__counter"), SN.cache.$body.find(".js__mosaic__single")),
            o = !1;
        if (0 < a.length) {
            var s = a.data("lat"),
                r = a.data("lon");

            // Esperar una mica per assegurar que el DOM està completament carregat
            setTimeout(function() {
                try {
                    singleMap = new GMaps({
                        div: ".js__mosaic__single",
                        lat: s,
                        lng: r,
                        zoom: 18,
                        zoomControl: !0,
                        fitZoom: !0
                    });

                    console.log('🗺️ Mapa individual inicialitzat');

                    // Afegir marker al mapa individual
                    singleMap.addMarker({
                        lat: s,
                        lng: r,
                        icon: theia.singleMap.icon
                    });

                } catch (error) {
                    console.error('🗺️ Error inicialitzant mapa individual:', error);
                }
            }, 50);
        }
        // Verificar que l'element del mapa existeix abans de crear-lo
        if (0 < SN.map.canvas.length) {
            // Verificar que GMaps està disponible
            if (typeof GMaps === 'undefined') {
                console.error('🗺️ GMaps wrapper no està disponible');
                return;
            }

            // Esperar una mica per assegurar que el DOM està completament carregat
            setTimeout(function() {
                try {
                    o = new GMaps({
                        div: ".js__map",
                        lat: theia.map.center.lat,
                        lng: theia.map.center.long,
                        zoom: theia.map.zoom,
                        zoomControl: !0,
                        fitZoom: !0
                    });

                    console.log('🗺️ Mapa principal inicialitzat');
                } catch (error) {
                    console.error('🗺️ Error inicialitzant mapa principal:', error);
                }
            }, 100);
        }

        // Esperar que el mapa estigui inicialitzat abans de continuar
        setTimeout(function() {
            if (o && o.initialized) {
                // Guardar instància del mapa globalment
                SN.map.mapInstance = o;

                // Llegir filtres de la URL al carregar la pàgina
                SN.map.readFiltersFromURL(i, n, t);

                // Carregar markers amb filtres aplicats
                SN.map.getMarkers(o, true);

                // Carregar comptes inicials de filtres
                SN.map.updateFilterCounts(i, n, t);

                console.log('🗺️ Mapa principal completament configurat');
            } else {
                console.warn('🗺️ Mapa principal no inicialitzat, reintentant...');
                // Reintentar després d'una pausa
                setTimeout(arguments.callee, 200);
            }
        }, 200);

        // Inicialitzar gestió de canvis de vista
        SN.map.initViewSwitcher();

        e.on("change", function (e) {
            e.preventDefault(), 0 != o && (SN.map.ajaxCall.district = i.val(), SN.map.ajaxCall.technique = n.val(), SN.map.ajaxCall.period = t.val(), SN.map.getMarkers(o), SN.map.updateFilterCounts(i, n, t), SN.map.updateURL(i.val(), n.val(), t.val(), ''))
        });

        // Listener per als canvis de hash (botó enrere/endavant) - NOMÉS per al mapa
        $(window).on('hashchange.map', function() {
            // Només executar si estem a la pàgina del mapa
            if (SN.map.canvas.length > 0) {
                console.log("🔄 Hash canviat, actualitzant mapa...");
                // Llegir nous filtres de la URL i actualitzar selects visualment
                SN.map.readFiltersFromURL(i, n, t, true);

                // Forçar actualització del mapa amb els nous filtres
                console.log("🗺️ Forçant actualització del mapa amb filtres:", SN.map.ajaxCall);
                console.log("🗺️ Instància del mapa:", SN.map.mapInstance);

                if (SN.map.mapInstance) {
                    SN.map.getMarkers(SN.map.mapInstance);
                } else {
                    console.warn("⚠️ No hi ha instància del mapa disponible");
                }
            }
        });

        0 < SN.map.itineraryMap.length && SN.map.getItineraryMap()
    },
    getMarkers: function (i, isInitialLoad = false) {
        console.log("🗺️ getMarkers cridat amb ajaxCall:", SN.map.ajaxCall);
        $.ajax({
            dataType: "json",
            type: "GET",
            url: theia.ajaxUrl,
            data: SN.map.ajaxCall,
            beforeSend: function (e) {
                // Mostrar spinner modern i controlar manualment
                SN.map.loading.fadeIn(1e3);
                var spinner = SN.map.loading.find('.map-spinner')[0];
                if (spinner && spinner.mapSpinnerInstance) {
                    // Reiniciar spinner i començar progressió manual
                    spinner.mapSpinnerInstance.reset();
                    spinner.mapSpinnerInstance.setPercentage(10); // Inici de petició
                }
            }
        }).done(function (e) {
            console.log(e);

            var spinner = SN.map.loading.find('.map-spinner')[0];
            if (spinner && spinner.mapSpinnerInstance) {
                // Progressió durant el processament
                spinner.mapSpinnerInstance.setPercentage(60); // Dades rebudes

                setTimeout(function() {
                    // Netejar tot abans de carregar nous markers
                    console.log("🧹 Netejant mapa abans de carregar nous markers...");

                    // Eliminar tots els clusters manuals anteriors
                    SN.map.clearClusterMarkers();

                    // Eliminar clusterer oficial si existeix
                    if (SN.map.markerClusterer) {
                        if (typeof SN.map.markerClusterer.clearMarkers === 'function') {
                            SN.map.markerClusterer.clearMarkers();
                        }
                        SN.map.markerClusterer = null;
                    }

                    // Netejar array de markers guardats
                    SN.map.allMarkers = [];

                    // Eliminar markers existents del mapa
                    i.removeMarkers();

                    spinner.mapSpinnerInstance.setPercentage(80); // Netejant mapa

                    if (e.markers && 0 < e.markers.length) {
                        setTimeout(function() {
                            console.log("🗺️ Afegint " + e.markers.length + " markers amb clustering...");
                            console.log("🔍 DEBUG RESPOSTA BACKEND COMPLETA:", e); // Debug per veure què rep el frontend
                            console.log("🔍 INVALID_MOSAICS ESPECÍFIC:", e.invalid_mosaics); // Debug específic per invalid_mosaics

                            // Guardar la resposta per usar-la a la finestreta de mosaics invàlids
                            window.lastMapResponse = e;

                            // DEBUG ESPECÍFIC PER INVALID_MOSAICS
                            console.log("🔍 DEBUG VERSION:", e.debug_version);
                            console.log("🔍 DEBUG TIMESTAMP:", e.debug_timestamp);
                            console.log("🔍 INVALID_MOSAICS EN RESPOSTA:", e.invalid_mosaics);
                            console.log("🔍 LONGITUD INVALID_MOSAICS:", e.invalid_mosaics ? e.invalid_mosaics.length : 'UNDEFINED');

                            // LOG FORÇAT SIMPLE
                            console.warn("=== DEBUG COMPLET ===");
                            console.warn("e.count:", e.count);
                            console.warn("e.markers.length:", e.markers.length);
                            console.warn("e.invalid_mosaics:", e.invalid_mosaics);
                            console.warn("typeof e.invalid_mosaics:", typeof e.invalid_mosaics);

                            // MOSTRAR LLISTA DE MOSAICS INVÀLIDS
                            if (e.invalid_mosaics && e.invalid_mosaics.length > 0) {
                                console.warn("✅ INVALID_MOSAICS EXISTEIX! Longitud:", e.invalid_mosaics.length);
                                console.warn("📋 LLISTA COMPLETA DE MOSAICS INVÀLIDS:");
                                for (var debug_i = 0; debug_i < e.invalid_mosaics.length; debug_i++) {
                                    var mosaic = e.invalid_mosaics[debug_i];
                                    var locationInfo = "Coordenades invàlides";

                                    if (mosaic.location) {
                                        if (typeof mosaic.location === 'object') {
                                            var lat = mosaic.location.lat || 'MISSING';
                                            var lng = mosaic.location.lng || 'MISSING';
                                            locationInfo = "lat: " + lat + ", lng: " + lng;
                                        } else {
                                            locationInfo = "No és un objecte vàlid";
                                        }
                                    }

                                    console.warn("🔸 ID: " + mosaic.id + " | \"" + mosaic.title + "\" | " + locationInfo);
                                }
                            } else if (e.invalid_mosaics && e.invalid_mosaics.length === 0) {
                                console.log("✅ Tots els mosaics tenen coordenades vàlides");
                            } else {
                                console.error("❌ INVALID_MOSAICS NO EXISTEIX AL BACKEND!");

                                // Calcular diferència
                                var excluded_count = e.count - e.markers.length;
                                if (excluded_count > 0) {
                                    console.warn("📋 MOSAICS EXCLOSOS DETECTATS (calculat):");
                                    console.warn("🔸 " + excluded_count + " mosaics exclosos per coordenades invàlides");
                                    console.warn("🔸 El backend no envia la llista detallada - consulta els logs del servidor");
                                }
                            }
                            console.warn("=== FI DEBUG ===");

                            // Afegir botó per veure mosaics invàlids només si hi ha mosaics invàlids i usuari registrat
                            if (e.invalid_mosaics && e.invalid_mosaics.length > 0) {
                                SN.map.addInvalidMosaicsButton();
                            } else {
                                // Eliminar el botó si no hi ha mosaics invàlids
                                SN.map.removeInvalidMosaicsButton();
                            }

                            // Afegir markers directament amb Leaflet
                            if (i && i.addMarkers) {
                                i.addMarkers(e.markers);
                            }
                            SN.map.counter.html(e.count);

                            // Guardar el total count i mosaics invàlids per usar en clustering dinàmic
                            SN.map.lastTotalCount = e.count;
                            SN.map.lastInvalidMosaics = e.invalid_mosaics || [];

                            // Mostrar informació detallada de mosaics exclosos amb logs directes
                            if (e.invalid_mosaics && e.invalid_mosaics.length > 0) {
                                console.warn("📋 MOSAICS EXCLOSOS DETECTATS:");
                                for (var k = 0; k < e.invalid_mosaics.length; k++) {
                                    var mosaic = e.invalid_mosaics[k];
                                    var locationInfo = "Coordenades invàlides";

                                    if (mosaic.location) {
                                        if (typeof mosaic.location === 'object') {
                                            var lat = mosaic.location.lat || 'MISSING';
                                            var lng = mosaic.location.lng || 'MISSING';
                                            locationInfo = "lat: " + lat + ", lng: " + lng;
                                        } else {
                                            locationInfo = "No és un objecte vàlid";
                                        }
                                    }

                                    console.warn("🔸 ID: " + mosaic.id + " | \"" + mosaic.title + "\" | " + locationInfo);
                                }
                            } else {
                                console.log("✅ Tots els mosaics tenen coordenades vàlides");
                            }

                            // Guardar NOMÉS els markers filtrats per clustering dinàmic
                            SN.map.allMarkers = [];
                            for (var j = 0; j < i.markers.length; j++) {
                                SN.map.allMarkers.push(i.markers[j]);
                            }
                            console.log("💾 Guardats " + SN.map.allMarkers.length + " markers filtrats per clustering");

                            // Debug: Verificar disponibilitat de MarkerClusterer
                            console.log("🔍 Verificant MarkerClusterer:", {
                                'typeof MarkerClusterer': typeof MarkerClusterer,
                                'typeof google.maps.marker': typeof google.maps.marker,
                                'typeof markerClusterer': typeof markerClusterer,
                                'window.MarkerClusterer': typeof window.MarkerClusterer,
                                'google.maps.marker.AdvancedMarkerElement': typeof google.maps.marker?.AdvancedMarkerElement
                            });

                            // CLUSTERING DINÀMIC DESACTIVAT TEMPORALMENT (Google Maps dependencies)
                            console.log("🔗 Clustering dinàmic desactivat temporalment - usant clustering de Leaflet");
                            // SN.map.setupDynamicClustering(i);

                            // Fer zoom per encabir tots els markers
                            SN.map.fitBounds(i, e.markers);
                            // Amagar missatge de no resultats si existeix
                            SN.map.hideNoResults();

                            spinner.mapSpinnerInstance.setPercentage(100); // Markers afegits

                            // Completar després d'un petit delay
                            setTimeout(function() {
                                spinner.mapSpinnerInstance.complete(function() {
                                    SN.map.loading.fadeOut(300);
                                });
                            }, 200);
                        }, 300);
                    } else {
                        console.info("No se han encontrado puntos para el mapa");

                        // Eliminar el botó de mosaics invàlids quan no hi ha resultats
                        SN.map.removeInvalidMosaicsButton();

                        // Actualitzar contador a 0
                        SN.map.counter.html(0);
                        // Mostrar missatge de no resultats
                        SN.map.showNoResults();
                        spinner.mapSpinnerInstance.setPercentage(100); // Completat

                        setTimeout(function() {
                            spinner.mapSpinnerInstance.complete(function() {
                                SN.map.loading.fadeOut(300);
                            });
                        }, 200);
                    }
                }, 200);
            } else {
                // Fallback sense spinner
                console.log("🧹 Netejant mapa abans de carregar nous markers (fallback)...");

                // Eliminar tots els clusters manuals anteriors
                SN.map.clearClusterMarkers();

                // Eliminar clusterer oficial si existeix
                if (SN.map.markerClusterer) {
                    if (typeof SN.map.markerClusterer.clearMarkers === 'function') {
                        SN.map.markerClusterer.clearMarkers();
                    }
                    SN.map.markerClusterer = null;
                }

                // Netejar array de markers guardats
                SN.map.allMarkers = [];

                // Eliminar markers existents del mapa
                i.removeMarkers();

                if (e.markers && 0 < e.markers.length) {
                    console.log("🗺️ Afegint " + e.markers.length + " markers amb clustering (fallback)...");
                    console.log("🔍 DEBUG RESPOSTA BACKEND FALLBACK:", e); // Debug fallback
                    console.log("🔍 INVALID_MOSAICS FALLBACK:", e.invalid_mosaics); // Debug específic fallback

                    i.addMarkers(e.markers);
                    SN.map.counter.html(e.count);

                    // Guardar el total count i mosaics invàlids per usar en clustering dinàmic (fallback)
                    SN.map.lastTotalCount = e.count;
                    SN.map.lastInvalidMosaics = e.invalid_mosaics || [];

                    // Afegir botó per veure mosaics invàlids només si hi ha mosaics invàlids i usuari registrat (fallback)
                    if (e.invalid_mosaics && e.invalid_mosaics.length > 0) {
                        SN.map.addInvalidMosaicsButton();
                    } else {
                        // Eliminar el botó si no hi ha mosaics invàlids
                        SN.map.removeInvalidMosaicsButton();
                    }

                    // Mostrar informació detallada de mosaics exclosos amb logs directes (fallback)
                    if (e.invalid_mosaics && e.invalid_mosaics.length > 0) {
                        console.warn("📋 MOSAICS EXCLOSOS DETECTATS (FALLBACK):");
                        for (var k = 0; k < e.invalid_mosaics.length; k++) {
                            var mosaic = e.invalid_mosaics[k];
                            var locationInfo = "Coordenades invàlides";

                            if (mosaic.location) {
                                if (typeof mosaic.location === 'object') {
                                    var lat = mosaic.location.lat || 'MISSING';
                                    var lng = mosaic.location.lng || 'MISSING';
                                    locationInfo = "lat: " + lat + ", lng: " + lng;
                                } else {
                                    locationInfo = "No és un objecte vàlid";
                                }
                            }

                            console.warn("🔸 ID: " + mosaic.id + " | \"" + mosaic.title + "\" | " + locationInfo);
                        }
                    } else {
                        console.log("✅ Tots els mosaics tenen coordenades vàlides (fallback)");
                    }

                    // Guardar NOMÉS els markers filtrats per clustering dinàmic (fallback)
                    SN.map.allMarkers = [];
                    for (var j = 0; j < i.markers.length; j++) {
                        SN.map.allMarkers.push(i.markers[j]);
                    }
                    console.log("💾 Guardats " + SN.map.allMarkers.length + " markers filtrats per clustering (fallback)");

                    // NOMÉS usar clustering dinàmic manual (fallback)
                    console.log("🔗 Configurant clustering dinàmic (fallback) per " + e.markers.length + " markers");
                    SN.map.setupDynamicClustering(i);

                    // Fer zoom per encabir tots els markers
                    SN.map.fitBounds(i, e.markers);
                    // Amagar missatge de no resultats si existeix
                    SN.map.hideNoResults();
                } else {
                    // Eliminar el botó de mosaics invàlids quan no hi ha resultats (fallback)
                    SN.map.removeInvalidMosaicsButton();

                    // Actualitzar contador a 0
                    SN.map.counter.html(0);
                    // Mostrar missatge de no resultats
                    SN.map.showNoResults();
                }
                SN.map.loading.delay(500).fadeOut(300);
            }
        }).fail(function (e, textStatus, errorThrown) {
            console.error("jQuery XHR: " + e + "\nText Status: " + textStatus + "\nError: " + errorThrown);

            // En cas d'error, netejar tot i mostrar missatge
            console.log("🧹 Netejant mapa per error...");

            // Eliminar tots els clusters manuals anteriors
            SN.map.clearClusterMarkers();

            // Eliminar clusterer oficial si existeix
            if (SN.map.markerClusterer) {
                if (typeof SN.map.markerClusterer.clearMarkers === 'function') {
                    SN.map.markerClusterer.clearMarkers();
                }
                SN.map.markerClusterer = null;
            }

            // Netejar array de markers guardats
            SN.map.allMarkers = [];

            // Eliminar markers existents del mapa
            i.removeMarkers();
            SN.map.counter.html(0);
            SN.map.showNoResults();

            // Eliminar el botó de mosaics invàlids en cas d'error
            SN.map.removeInvalidMosaicsButton();

            // Gestionar error en el spinner
            var spinner = SN.map.loading.find('.map-spinner')[0];
            if (spinner && spinner.mapSpinnerInstance) {
                spinner.mapSpinnerInstance.complete(function() {
                    SN.map.loading.fadeOut(300);
                });
            } else {
                SN.map.loading.fadeOut(300);
            }
        })
    },
    fitBounds: function (mapInstance, markers) {
        // Funció per fer zoom automàtic per encabir tots els markers
        if (!mapInstance || !markers || markers.length === 0) {
            return;
        }

        try {
            if (markers.length === 1) {
                // Si només hi ha un marker, centrar-lo amb zoom adequat
                mapInstance.setCenter(markers[0].lat, markers[0].lng);
                mapInstance.setZoom(16);
            } else {
                // Si hi ha múltiples markers, calcular bounds correctament
                var bounds = new google.maps.LatLngBounds();

                // Afegir cada marker als bounds
                for (var i = 0; i < markers.length; i++) {
                    if (markers[i].lat && markers[i].lng) {
                        var latLng = new google.maps.LatLng(markers[i].lat, markers[i].lng);
                        bounds.extend(latLng);
                    }
                }

                if (!bounds.isEmpty()) {
                    // Utilitzar fitBounds natiu de Google Maps
                    mapInstance.map.fitBounds(bounds);

                    // Afegir padding per millor visualització
                    setTimeout(function() {
                        var currentZoom = mapInstance.getZoom();

                        // Limitar zoom màxim per evitar estar massa a prop
                        if (currentZoom > 17) {
                            mapInstance.setZoom(17);
                        }
                        // Assegurar zoom mínim per context
                        if (currentZoom < 10) {
                            mapInstance.setZoom(10);
                        }

                        // Afegir una mica de padding reduint el zoom
                        if (markers.length > 1 && currentZoom > 12) {
                            mapInstance.setZoom(currentZoom - 1);
                        }
                    }, 200);
                }
            }
        } catch (error) {
            console.warn('Error aplicant fitBounds:', error);
            // Fallback: centrar al primer marker
            if (markers.length > 0 && markers[0].lat && markers[0].lng) {
                mapInstance.setCenter(markers[0].lat, markers[0].lng);
                mapInstance.setZoom(14);
            }
        }
    },
    showNoResults: function() {
        // Eliminar missatge anterior
        $('.map__no-results').remove();

        // Crear missatge simple amb flex vertical
        var html = '<div class="map__no-results" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 40px; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); text-align: center; z-index: 9999; opacity: 1; display: flex; flex-direction: column; gap: 16px;">' +
            '<div style="font-size: 18px; font-weight: bold; color: #333;">No s\'han trobat resultats coincidents</div>' +
            '<div style="font-size: 14px; color: #666;">Prova a modificar els filtres de cerca</div>' +
            '</div>';

        // Afegir al wrapper del mapa
        SN.map.canvas.closest('.wrapper').append(html);
    },
    hideNoResults: function() {
        // Eliminar missatge de no resultats
        $('.map__no-results').remove();
    },
    readFiltersFromURL: function(districtSelect, techniqueSelect, periodSelect, skipTrigger = false) {
        // Llegir filtres de la URL (hash) similar a l'inventari
        var hash = window.location.hash;
        console.log("📖 Llegint filtres de la URL:", hash);

        if (hash && hash.length > 1) {
            try {
                // Decodificar hash: #MjMtMTMtMjkt (base64)
                var hashParts = hash.split("#");
                if (hashParts.length > 1 && hashParts[1].length > 0) {
                    var hashValue = hashParts[1];
                    console.log("🔍 Hash value a decodificar:", hashValue);

                    // Verificar que el hash sembla base64 vàlid
                    if (!/^[A-Za-z0-9+/]*={0,2}$/.test(hashValue)) {
                        console.warn("⚠️ Hash no sembla base64 vàlid:", hashValue);
                        throw new Error("Hash format invalid");
                    }

                    var decoded = atob(hashValue);
                    console.log("🔓 Hash decodificat:", decoded);
                    var filters = decoded.split("-");
                    console.log("📋 Filtres separats:", filters);

                    // Aplicar filtres si té el format correcte (district-technique-period-keyword)
                    if (filters.length >= 3) {
                        var district = parseInt(filters[0]) || 0;
                        var technique = parseInt(filters[1]) || 0;
                        var period = parseInt(filters[2]) || 0;

                        console.log("🎯 Filtres parseats:", {district: district, technique: technique, period: period});

                        // Actualitzar selects sense triggerar events si skipTrigger és true
                        if (skipTrigger) {
                            districtSelect.val(district).trigger('change.select2');
                            techniqueSelect.val(technique).trigger('change.select2');
                            periodSelect.val(period).trigger('change.select2');
                        } else {
                            districtSelect.val(district);
                            techniqueSelect.val(technique);
                            periodSelect.val(period);
                        }

                        // Actualitzar ajaxCall
                        SN.map.ajaxCall.district = district;
                        SN.map.ajaxCall.technique = technique;
                        SN.map.ajaxCall.period = period;

                        console.log("✅ Filtres aplicats correctament!");
                        return true; // Indica que s'han aplicat filtres
                    } else {
                        console.warn("⚠️ Format de filtres incorrecte, esperats >= 3, rebuts:", filters.length);
                    }
                }
            } catch (error) {
                console.error("❌ Error decodificant hash:", error);
                console.log("🔄 Aplicant filtres per defecte...");
                // Si hi ha error, usar valors per defecte
                SN.map.ajaxCall.district = 0;
                SN.map.ajaxCall.technique = 0;
                SN.map.ajaxCall.period = 0;
            }
        } else {
            // Sense hash, usar valors per defecte
            console.log("📝 Sense hash, aplicant filtres per defecte");
            SN.map.ajaxCall.district = 0;
            SN.map.ajaxCall.technique = 0;
            SN.map.ajaxCall.period = 0;

            if (skipTrigger) {
                districtSelect.val(0).trigger('change.select2');
                techniqueSelect.val(0).trigger('change.select2');
                periodSelect.val(0).trigger('change.select2');
            } else {
                districtSelect.val(0);
                techniqueSelect.val(0);
                periodSelect.val(0);
            }
        }
        return false; // Indica que no s'han aplicat filtres específics
    },
    updateFilterCounts: function(districtSelect, techniqueSelect, periodSelect) {
        // Evitar crides múltiples amb debounce
        if (SN.map.filterCountsTimeout) {
            clearTimeout(SN.map.filterCountsTimeout);
        }

        SN.map.filterCountsTimeout = setTimeout(function() {
            // Actualitzar comptes de filtres
            console.log("🔢 Actualitzant comptes de filtres...");

            var ajaxData = {
                action: "theia__get_filter_counts",
                security: theia.ajaxNonce,
                district: districtSelect.val() || 0,
                technique: techniqueSelect.val() || 0,
                period: periodSelect.val() || 0
            };

        $.ajax({
            dataType: "json",
            type: "GET",
            url: theia.ajaxUrl,
            data: ajaxData
        }).done(function(response) {
            console.log("📊 Comptes rebuts:", response);

            // Actualitzar opcions de district
            districtSelect.find('option').each(function() {
                var termId = parseInt($(this).val());
                if (termId > 0 && response.districts[termId]) {
                    var count = response.districts[termId].count;
                    var originalText = response.districts[termId].name;

                    // Actualitzar text amb compte
                    $(this).text(originalText + ' (' + count + ')');

                    // Inhabilitar si 0 resultats
                    if (count === 0) {
                        $(this).prop('disabled', true);
                    } else {
                        $(this).prop('disabled', false);
                    }
                }
            });

            // Actualitzar opcions de technique
            techniqueSelect.find('option').each(function() {
                var termId = parseInt($(this).val());
                if (termId > 0 && response.techniques[termId]) {
                    var count = response.techniques[termId].count;
                    var originalText = response.techniques[termId].name;

                    // Actualitzar text amb compte
                    $(this).text(originalText + ' (' + count + ')');

                    // Inhabilitar si 0 resultats
                    if (count === 0) {
                        $(this).prop('disabled', true);
                    } else {
                        $(this).prop('disabled', false);
                    }
                }
            });

            // Actualitzar opcions de period
            periodSelect.find('option').each(function() {
                var termId = parseInt($(this).val());
                if (termId > 0 && response.periods[termId]) {
                    var count = response.periods[termId].count;
                    var originalText = response.periods[termId].name;

                    // Actualitzar text amb compte
                    $(this).text(originalText + ' (' + count + ')');

                    // Inhabilitar si 0 resultats
                    if (count === 0) {
                        $(this).prop('disabled', true);
                    } else {
                        $(this).prop('disabled', false);
                    }
                }
            });

            // Actualitzar Select2 si està actiu
            if (districtSelect.hasClass('select2-hidden-accessible')) {
                districtSelect.trigger('change.select2');
            }
            if (techniqueSelect.hasClass('select2-hidden-accessible')) {
                techniqueSelect.trigger('change.select2');
            }
            if (periodSelect.hasClass('select2-hidden-accessible')) {
                periodSelect.trigger('change.select2');
            }

            console.log("✅ Comptes de filtres actualitzats!");

        }).fail(function(xhr, status, error) {
            console.error("❌ Error obtenint comptes de filtres:", error);
        });

        }, 300); // Debounce de 300ms
    },
    initViewSwitcher: function() {
        // Gestionar canvis de vista (llista ↔ mapa) mantenint filtres
        $('.js__filter__view-link').on('click', function(e) {
            e.preventDefault();

            var targetUrl = $(this).attr('href');
            var currentHash = window.location.hash;

            console.log("🔄 Canviant vista, hash actual:", currentHash);
            console.log("🎯 URL destí:", targetUrl);

            // Si hi ha filtres aplicats (hash), afegir-los a la nova URL
            if (currentHash && currentHash.length > 1) {
                // Construir nova URL amb hash
                var newUrl = targetUrl + currentHash;
                console.log("✅ Nova URL amb filtres:", newUrl);
                window.location.href = newUrl;
            } else {
                // Sense filtres, anar directament
                console.log("📝 Sense filtres, anant directament");
                window.location.href = targetUrl;
            }
        });
    },
    updateURL: function(district, technique, period, keyword) {
        // Actualitzar URL amb filtres (similar a l'inventari)
        var filters = district + "-" + technique + "-" + period + "-" + keyword;
        var encodedFilters = btoa(filters);

        console.log("🔗 Actualitzant URL amb filtres:", filters);

        // Actualitzar hash sense recarregar la pàgina
        if (window.history && window.history.replaceState) {
            var newUrl = window.location.pathname + window.location.search + '#' + encodedFilters;
            window.history.replaceState(null, null, newUrl);
        } else {
            // Fallback per navegadors antics
            window.location.hash = encodedFilters;
        }
    },
    implementSimpleClustering: function(mapInstance, markers) {
        // Clustering manual simple com a fallback
        console.log("🔧 Implementant clustering manual per " + markers.length + " markers");

        // Agrupar markers per proximitat (clustering simple)
        var clusters = [];
        var clusterDistance = 1; // Distància en km

        for (var i = 0; i < markers.length; i++) {
            var marker = markers[i];
            var position = marker.getPosition();
            var addedToCluster = false;

            // Buscar cluster existent proper
            for (var j = 0; j < clusters.length; j++) {
                var cluster = clusters[j];

                // Calcular distància simple (sense geometry library)
                var lat1 = position.lat();
                var lng1 = position.lng();
                var lat2 = cluster.center.lat();
                var lng2 = cluster.center.lng();

                var distance = Math.sqrt(Math.pow(lat1 - lat2, 2) + Math.pow(lng1 - lng2, 2)) * 111; // Aprox km

                if (distance < clusterDistance) {
                    cluster.markers.push(marker);
                    addedToCluster = true;
                    break;
                }
            }

            // Si no s'ha afegit a cap cluster, crear-ne un de nou
            if (!addedToCluster) {
                clusters.push({
                    center: position,
                    markers: [marker]
                });
            }
        }

        console.log("📊 Clustering manual: " + markers.length + " markers en " + clusters.length + " clusters");

        // Crear markers de cluster per agrupacions
        for (var k = 0; k < clusters.length; k++) {
            var cluster = clusters[k];

            if (cluster.markers.length > 1) {
                // Amagar markers individuals
                for (var m = 0; m < cluster.markers.length; m++) {
                    cluster.markers[m].setVisible(false);
                }

                // Crear marker de cluster
                var clusterMarker = new google.maps.Marker({
                    position: cluster.center,
                    map: mapInstance.map,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(
                            '<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 40 40">' +
                            '<circle cx="20" cy="20" r="18" fill="#f85c00" stroke="white" stroke-width="2"/>' +
                            '<text x="20" y="20" text-anchor="middle" dy="0.3em" fill="white" font-family="Arial, sans-serif" font-size="12" font-weight="bold">' + cluster.markers.length + '</text>' +
                            '</svg>'
                        ),
                        scaledSize: new google.maps.Size(40, 40),
                        anchor: new google.maps.Point(20, 20)
                    }
                });

                // Event per expandir cluster (closure per mantenir referència)
                (function(clusterMarkers, clusterMarkerRef) {
                    clusterMarkerRef.addListener('click', function() {
                        // Mostrar markers individuals
                        for (var n = 0; n < clusterMarkers.length; n++) {
                            clusterMarkers[n].setVisible(true);
                        }
                        // Amagar cluster marker
                        clusterMarkerRef.setVisible(false);

                        // Fer zoom a la zona
                        var bounds = new google.maps.LatLngBounds();
                        for (var o = 0; o < clusterMarkers.length; o++) {
                            bounds.extend(clusterMarkers[o].getPosition());
                        }
                        mapInstance.map.fitBounds(bounds);
                    });
                })(cluster.markers, clusterMarker);
            }
        }

        console.log("✅ Clustering manual implementat");
    },
    setupDynamicClustering: function(mapInstance) {
        // FUNCIÓ DESACTIVADA TEMPORALMENT - DEPENDENCIES DE GOOGLE MAPS
        console.log("🔧 setupDynamicClustering desactivat temporalment");
        return;

        /* FUNCIÓ ORIGINAL COMENTADA
        // Configurar clustering dinàmic que respongui als canvis de zoom
        console.log("🔧 Configurant clustering dinàmic...");

        // Afegir listener per canvis de zoom (Leaflet)
        if (mapInstance.map && mapInstance.map.on) {
            mapInstance.map.on('zoomend', function() {
                var currentZoom = mapInstance.map.getZoom();
                console.log("🔍 Zoom canviat a:", currentZoom);

            // Debounce per evitar massa crides
            if (SN.map.zoomTimeout) {
                clearTimeout(SN.map.zoomTimeout);
            }

            SN.map.zoomTimeout = setTimeout(function() {
                SN.map.recalculateClustering(mapInstance, currentZoom);
            }, 300);
            });
        }

        console.log("✅ Clustering dinàmic configurat");
        FI FUNCIÓ ORIGINAL COMENTADA */
    },
    recalculateClustering: function(mapInstance, zoomLevel) {
        // Recalcular clustering basat en el nivell de zoom
        console.log("🔄 Recalculant clustering per zoom:", zoomLevel);

        // Netejar clusters anteriors
        SN.map.clearClusterMarkers();

        // Mostrar tots els markers originals primer
        for (var i = 0; i < SN.map.allMarkers.length; i++) {
            SN.map.allMarkers[i].setVisible(true);
        }

        // Decidir si fer clustering basat en el zoom amb distàncies més grans
        if (zoomLevel <= 10 && SN.map.allMarkers.length > 1) {
            // Zoom molt llunyà: clustering molt agressiu
            console.log("📍 Zoom molt llunyà (" + zoomLevel + "), clustering agressiu");
            SN.map.implementDynamicClustering(mapInstance, SN.map.allMarkers, 10); // 10km de distància
        } else if (zoomLevel <= 12 && SN.map.allMarkers.length > 1) {
            // Zoom llunyà: clustering agressiu
            console.log("📍 Zoom llunyà (" + zoomLevel + "), clustering agressiu");
            SN.map.implementDynamicClustering(mapInstance, SN.map.allMarkers, 5); // 5km de distància
        } else if (zoomLevel <= 14 && SN.map.allMarkers.length > 3) {
            // Zoom mitjà: clustering moderat
            console.log("📍 Zoom mitjà (" + zoomLevel + "), clustering moderat");
            SN.map.implementDynamicClustering(mapInstance, SN.map.allMarkers, 2); // 2km de distància
        } else if (zoomLevel <= 16 && SN.map.allMarkers.length > 5) {
            // Zoom proper: clustering lleuger
            console.log("📍 Zoom proper (" + zoomLevel + "), clustering lleuger");
            SN.map.implementDynamicClustering(mapInstance, SN.map.allMarkers, 0.5); // 0.5km de distància
        } else {
            // Zoom molt proper: sense clustering
            console.log("📍 Zoom molt proper (" + zoomLevel + "), sense clustering");
        }
    },
    clearClusterMarkers: function() {
        // Eliminar tots els markers de cluster creats
        console.log("🧹 Netejant " + SN.map.clusterMarkers.length + " cluster markers...");

        for (var i = 0; i < SN.map.clusterMarkers.length; i++) {
            try {
                if (SN.map.clusterMarkers[i] && typeof SN.map.clusterMarkers[i].setMap === 'function') {
                    SN.map.clusterMarkers[i].setMap(null);
                }
            } catch (error) {
                console.warn("⚠️ Error eliminant cluster marker:", error);
            }
        }
        SN.map.clusterMarkers = [];

        console.log("✅ Cluster markers netejats");
    },
    implementDynamicClustering: function(mapInstance, markers, clusterDistance) {
        // Implementació de clustering dinàmic amb gestió de markers solapats
        var clusters = [];
        var processedMarkers = []; // Tracking de markers ja processats

        // Primer: Agrupar markers per posició exacta (solapats)
        var positionGroups = {};
        for (var i = 0; i < markers.length; i++) {
            var marker = markers[i];
            var pos = marker.getPosition();
            var key = pos.lat().toFixed(6) + ',' + pos.lng().toFixed(6); // Clau única per posició

            if (!positionGroups[key]) {
                positionGroups[key] = [];
            }
            positionGroups[key].push(marker);
        }

        console.log("📍 Detectats " + Object.keys(positionGroups).length + " posicions úniques de " + markers.length + " markers");

        // Crear clusters inicials per cada posició única
        for (var key in positionGroups) {
            var markersAtPosition = positionGroups[key];
            if (markersAtPosition.length > 0) {
                var firstMarker = markersAtPosition[0];
                clusters.push({
                    center: firstMarker.getPosition(),
                    markers: markersAtPosition.slice() // Copia de l'array
                });

                // Marcar tots com processats
                for (var j = 0; j < markersAtPosition.length; j++) {
                    processedMarkers.push(markersAtPosition[j]);
                }

                if (markersAtPosition.length > 1) {
                    console.log("🔄 Posició " + key + " té " + markersAtPosition.length + " markers solapats");
                }
            }
        }

        // Segon: Agrupar clusters propers segons clusterDistance
        var finalClusters = [];
        var processedClusters = [];

        for (var i = 0; i < clusters.length; i++) {
            var cluster = clusters[i];

            if (processedClusters.indexOf(cluster) !== -1) {
                continue;
            }

            var mergedCluster = {
                center: cluster.center,
                markers: cluster.markers.slice()
            };
            processedClusters.push(cluster);

            // Buscar altres clusters per fusionar
            for (var j = i + 1; j < clusters.length; j++) {
                var otherCluster = clusters[j];

                if (processedClusters.indexOf(otherCluster) !== -1) {
                    continue;
                }

                // Calcular distància entre centres
                var lat1 = mergedCluster.center.lat();
                var lng1 = mergedCluster.center.lng();
                var lat2 = otherCluster.center.lat();
                var lng2 = otherCluster.center.lng();

                var distance = Math.sqrt(Math.pow(lat1 - lat2, 2) + Math.pow(lng1 - lng2, 2)) * 111; // Aprox km

                if (distance < clusterDistance) {
                    // Fusionar clusters
                    mergedCluster.markers = mergedCluster.markers.concat(otherCluster.markers);
                    processedClusters.push(otherCluster);

                    // Recalcular centre
                    var totalLat = 0;
                    var totalLng = 0;
                    for (var k = 0; k < mergedCluster.markers.length; k++) {
                        var pos = mergedCluster.markers[k].getPosition();
                        totalLat += pos.lat();
                        totalLng += pos.lng();
                    }
                    mergedCluster.center = new google.maps.LatLng(
                        totalLat / mergedCluster.markers.length,
                        totalLng / mergedCluster.markers.length
                    );
                }
            }

            finalClusters.push(mergedCluster);
        }

        clusters = finalClusters;

        // Crear markers de cluster per agrupacions
        var totalClustered = 0;
        var totalClusters = 0;

        for (var k = 0; k < clusters.length; k++) {
            var cluster = clusters[k];

            if (cluster.markers.length > 1) {
                totalClusters++;
                totalClustered += cluster.markers.length;

                // Amagar markers individuals
                for (var m = 0; m < cluster.markers.length; m++) {
                    cluster.markers[m].setVisible(false);
                }

                // Calcular mida i color del cluster basat en el número de markers
                var count = cluster.markers.length;
                var size = count < 5 ? 35 : count < 10 ? 40 : count < 20 ? 45 : 50;
                var color = count < 5 ? '#f85c00' : count < 10 ? '#e04900' : count < 20 ? '#cc3300' : '#b32900';
                var fontSize = count < 10 ? '11' : count < 100 ? '10' : '9';

                // Crear marker de cluster amb estil dinàmic
                var clusterMarker = new google.maps.Marker({
                    position: cluster.center,
                    map: mapInstance.map,
                    icon: {
                        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(
                            '<svg xmlns="http://www.w3.org/2000/svg" width="' + size + '" height="' + size + '" viewBox="0 0 ' + size + ' ' + size + '">' +
                            '<circle cx="' + (size/2) + '" cy="' + (size/2) + '" r="' + (size/2-2) + '" fill="' + color + '" stroke="white" stroke-width="2"/>' +
                            '<text x="' + (size/2) + '" y="' + (size/2) + '" text-anchor="middle" dy="0.3em" fill="white" font-family="Arial, sans-serif" font-size="' + fontSize + '" font-weight="bold">' + count + '</text>' +
                            '</svg>'
                        ),
                        scaledSize: new google.maps.Size(size, size),
                        anchor: new google.maps.Point(size/2, size/2)
                    },
                    title: count + ' mosaics en aquesta zona'
                });

                // Guardar cluster marker per poder eliminar-lo després
                SN.map.clusterMarkers.push(clusterMarker);

                // Event per expandir cluster
                (function(clusterMarkers, clusterMarkerRef, clusterCenter) {
                    clusterMarkerRef.addListener('click', function() {
                        console.log("🔍 Expandint cluster amb " + clusterMarkers.length + " markers");

                        // Mostrar markers individuals
                        for (var n = 0; n < clusterMarkers.length; n++) {
                            clusterMarkers[n].setVisible(true);
                        }
                        // Amagar cluster marker
                        clusterMarkerRef.setVisible(false);

                        // Fer zoom a la zona amb padding adequat
                        var bounds = new google.maps.LatLngBounds();
                        for (var o = 0; o < clusterMarkers.length; o++) {
                            bounds.extend(clusterMarkers[o].getPosition());
                        }

                        // Afegir padding al bounds
                        var ne = bounds.getNorthEast();
                        var sw = bounds.getSouthWest();
                        var padding = 0.001; // Aprox 100m de padding

                        bounds.extend(new google.maps.LatLng(ne.lat() + padding, ne.lng() + padding));
                        bounds.extend(new google.maps.LatLng(sw.lat() - padding, sw.lng() - padding));

                        mapInstance.map.fitBounds(bounds);

                        // Limitar zoom màxim
                        setTimeout(function() {
                            if (mapInstance.getZoom() > 18) {
                                mapInstance.setZoom(18);
                            }
                        }, 100);
                    });
                })(cluster.markers, clusterMarker, cluster.center);
            }
        }

        var individualMarkers = markers.length - totalClustered;
        var overlappedMarkers = markers.length - Object.keys(positionGroups).length;

        console.log("🔄 Clustering dinàmic: " + markers.length + " markers → " + totalClusters + " clusters (" + totalClustered + " agrupats) + " + individualMarkers + " individuals");
        console.log("📍 Posicions úniques: " + Object.keys(positionGroups).length + " (markers solapats: " + overlappedMarkers + ")");
        console.log("✅ Verificació: " + processedMarkers.length + " markers processats (ha de coincidir amb " + markers.length + ")");

        // Mostrar informació sobre mosaics exclosos si n'hi ha
        if (SN.map.lastTotalCount && markers.length < SN.map.lastTotalCount) {
            var excluded = SN.map.lastTotalCount - markers.length;
            console.group("⚠️ Recordatori: Mosaics Exclosos del Filtre Actual");
            console.warn("📊 Total mosaics que compleixen els filtres: " + SN.map.lastTotalCount);
            console.warn("🗺️ Markers amb coordenades vàlides: " + markers.length);
            console.warn("❌ Mosaics exclosos per coordenades invàlides: " + excluded);

            // Mostrar detalls dels mosaics invàlids si estan disponibles
            if (SN.map.lastInvalidMosaics && SN.map.lastInvalidMosaics.length > 0) {
                console.warn("📋 Detall dels Mosaics Exclosos:");
                for (var i = 0; i < SN.map.lastInvalidMosaics.length; i++) {
                    var mosaic = SN.map.lastInvalidMosaics[i];
                    var locationInfo = "Coordenades invàlides";

                    if (mosaic.location) {
                        if (typeof mosaic.location === 'object') {
                            var lat = mosaic.location.lat || 'MISSING';
                            var lng = mosaic.location.lng || 'MISSING';
                            locationInfo = "lat: " + lat + ", lng: " + lng;
                        } else {
                            locationInfo = "No és un objecte vàlid";
                        }
                    }

                    console.warn("🔸 ID: " + mosaic.id + " | \"" + mosaic.title + "\" | " + locationInfo);
                }
            }

            console.info("💡 Aquests números es mantenen constants durant el clustering dinàmic");
            console.groupEnd();
        }

    },
    showExcludedMosaicsInfo: function(totalCount, markersCount, context, invalidMosaics) {
        if (markersCount < totalCount) {
            var excluded = totalCount - markersCount;
            var contextLabel = context ? ' (' + context + ')' : '';

            console.group("⚠️ Mosaics Exclosos" + contextLabel);
            console.warn("📊 Total mosaics filtrats: " + totalCount);
            console.warn("🗺️ Markers visibles al mapa: " + markersCount);
            console.warn("❌ Mosaics exclosos: " + excluded);
            console.warn("🔍 Motiu: Coordenades GPS invàlides o buides");

            // Mostrar detalls dels mosaics invàlids si estan disponibles
            if (invalidMosaics && invalidMosaics.length > 0) {
                console.warn("📋 Detall dels Mosaics Exclosos:");
                for (var i = 0; i < invalidMosaics.length; i++) {
                    var mosaic = invalidMosaics[i];
                    var locationInfo = "Coordenades invàlides";

                    if (mosaic.location) {
                        if (typeof mosaic.location === 'object') {
                            var lat = mosaic.location.lat || 'MISSING';
                            var lng = mosaic.location.lng || 'MISSING';
                            locationInfo = "lat: " + lat + ", lng: " + lng;
                        } else {
                            locationInfo = "No és un objecte vàlid";
                        }
                    }

                    console.warn("🔸 ID: " + mosaic.id + " | \"" + mosaic.title + "\" | " + locationInfo);
                }
            }

            console.groupEnd();

            // També mostrar un missatge simple
            console.log("📋 Resum: " + markersCount + " de " + totalCount + " mosaics mostrats al mapa" + contextLabel);
        }
    },
    addMarkersWithOverlapDetection: function(mapInstance, markers) {
        console.log("🔍 Funció de detecció de sobreposicions desactivada temporalment");

        // Fallback simple: només afegir markers
        if (mapInstance && mapInstance.addMarkers) {
            mapInstance.addMarkers(markers);
        }

        return;

        /* FUNCIÓ ORIGINAL COMENTADA TEMPORALMENT
        console.log("🔍 Detectant markers sobreposats per la query actual...");

        // Primer, netejar detectors anteriors
        if (mapInstance.overlapDetectors) {
            mapInstance.overlapDetectors.forEach(function(detector) {
                detector.setMap(null);
            });
        }
        mapInstance.overlapDetectors = [];

        // Agrupar NOMÉS els markers de la query actual per coordenades exactes
        var markerGroups = {};
        var overlappedMarkers = [];

        markers.forEach(function(marker, index) {
            var key = marker.lat + ',' + marker.lng;
            if (!markerGroups[key]) {
                markerGroups[key] = [];
            }
            markerGroups[key].push({
                marker: marker,
                index: index
            });
        });

        // Identificar grups amb més d'un marker (sobreposats) NOMÉS de la query actual
        Object.keys(markerGroups).forEach(function(key) {
            var group = markerGroups[key];
            if (group.length > 1) {
                console.log("🔍 Trobat grup sobreposat amb " + group.length + " markers de la query actual a " + key);
                overlappedMarkers.push({
                    coordinates: key,
                    markers: group
                });
            }
        });

        // Afegir tots els markers normalment primer
        mapInstance.addMarkers(markers);

        // Aplicar funcionalitat de desplegament NOMÉS per markers sobreposats de la query actual
        if (overlappedMarkers.length > 0) {
            console.log("✨ Aplicant funcionalitat de desplegament a " + overlappedMarkers.length + " grups sobreposats de la query actual");
            this.setupOverlapHandling(mapInstance, overlappedMarkers);
        }
    },
    setupOverlapHandling: function(mapInstance, overlappedGroups) {
        var self = this;

        overlappedGroups.forEach(function(group) {
            var coords = group.coordinates.split(',');
            var baseLat = parseFloat(coords[0]);
            var baseLng = parseFloat(coords[1]);
            var markersInGroup = group.markers;

            var isExpanded = false;
            var originalPositions = [];
            var actualMarkers = [];
            var connectionLines = [];

            // Trobar els markers reals de Google Maps corresponents
            markersInGroup.forEach(function(markerData, index) {
                // Buscar el marker real dins dels markers del mapa
                var realMarker = null;
                if (mapInstance.markers && mapInstance.markers.length > 0) {
                    mapInstance.markers.forEach(function(mapMarker) {
                        if (mapMarker && mapMarker.getLatLng) {
                            var markerPos = mapMarker.getLatLng();
                            if (Math.abs(markerPos.lat - baseLat) < 0.0001 &&
                                Math.abs(markerPos.lng - baseLng) < 0.0001 &&
                                !actualMarkers.includes(mapMarker)) {
                                realMarker = mapMarker;
                                actualMarkers.push(realMarker);
                                originalPositions.push({
                                    lat: baseLat,
                                    lng: baseLng
                                });
                                return false; // Sortir del bucle
                            }
                        }
                    });
                }
            });

            // Crear el detector de clicks utilitzant el wrapper de Leaflet
            var clickDetector = mapInstance.addOverlapDetector(
                baseLat,
                baseLng,
                actualMarkers,
                function(markers) {
                    // Separar els markers quan es fa click
                    mapInstance.separateOverlappingMarkers(markers, baseLat, baseLng);

                    // Eliminar el detector després d'usar-lo
                    if (mapInstance.map.hasLayer(clickDetector)) {
                        mapInstance.map.removeLayer(clickDetector);
                    }
                }
            );

            // Sincronitzar visibilitat amb els markers originals
            var syncVisibility = function() {
                var anyMarkerVisible = false;
                actualMarkers.forEach(function(marker) {
                    if (marker.getVisible && marker.getVisible()) {
                        anyMarkerVisible = true;
                    }
                });
                clickDetector.setVisible(anyMarkerVisible);
            };

            // Executar sincronització inicial
            syncVisibility();

            // Monitoritzar canvis de visibilitat dels markers originals
            actualMarkers.forEach(function(marker) {
                // Afegir listener per canvis de visibilitat si és possible
                if (marker.addListener) {
                    marker.addListener('visible_changed', syncVisibility);
                }
            });

            // Afegir el detector a la llista per poder netejar-lo després
            mapInstance.overlapDetectors.push(clickDetector);

            // Funcions per expandir i contraure
            var expandMarkers = function() {
                if (isExpanded || actualMarkers.length <= 1) return;

                console.log("✨ Expandint " + actualMarkers.length + " markers sobreposats");
                isExpanded = true;

                var radius = 0.0001; // Radi més petit per separació subtil
                var angleStep = (2 * Math.PI) / actualMarkers.length;

                actualMarkers.forEach(function(marker, index) {
                    var angle = index * angleStep;
                    var newLat = baseLat + (radius * Math.cos(angle));
                    var newLng = baseLng + (radius * Math.sin(angle));

                    // Crear línia de connexió des del centre
                    var connectionLine = new google.maps.Polyline({
                        path: [
                            { lat: baseLat, lng: baseLng },
                            { lat: newLat, lng: newLng }
                        ],
                        geodesic: false,
                        strokeColor: '#FF6B35',
                        strokeOpacity: 0.8,
                        strokeWeight: 2,
                        map: mapInstance.map,
                        zIndex: 1500
                    });

                    connectionLines.push(connectionLine);

                    // Moure marker a nova posició
                    marker.setPosition({ lat: newLat, lng: newLng });
                    marker.setZIndex(2000 + index);

                    // NO modificar la icona per evitar deformacions
                    // Els markers mantenen la seva aparença original
                });
            };

            var contractMarkers = function() {
                if (!isExpanded) return;

                console.log("🔄 Contraient markers sobreposats");
                isExpanded = false;

                // Eliminar línies de connexió
                connectionLines.forEach(function(line) {
                    line.setMap(null);
                });
                connectionLines = [];

                actualMarkers.forEach(function(marker, index) {
                    // Tornar a la posició original
                    marker.setPosition({ lat: baseLat, lng: baseLng });
                    marker.setZIndex(1000);

                    // NO modificar la icona - mantenir aparença original
                });
            };

            // Variable per controlar el listener del mapa
            var mapClickListener = null;

            // Events per click
            clickDetector.addListener('click', function() {
                if (!isExpanded) {
                    expandMarkers();
                    // Ocultar el detector després d'expandir
                    clickDetector.setMap(null);

                    // Crear listener del mapa per detectar clicks fora dels markers
                    mapClickListener = google.maps.event.addListener(mapInstance.map, 'click', function(event) {
                        if (isExpanded) {
                            var clickLat = event.latLng.lat();
                            var clickLng = event.latLng.lng();
                            var clickedOnMarker = false;

                            // Verificar si el click és sobre algun dels markers expandits
                            actualMarkers.forEach(function(marker) {
                                var markerPos = marker.getPosition();
                                var markerLat = markerPos.lat();
                                var markerLng = markerPos.lng();

                                // Calcular distància al marker (zona clickable aproximada)
                                var distanceToMarker = Math.sqrt(
                                    Math.pow(clickLat - markerLat, 2) +
                                    Math.pow(clickLng - markerLng, 2)
                                );

                                // Si el click és molt a prop d'un marker (zona clickable)
                                if (distanceToMarker < 0.00005) { // Zona molt petita al voltant del marker
                                    clickedOnMarker = true;
                                }
                            });

                            // Si no s'ha clicat sobre cap marker, contraure
                            if (!clickedOnMarker) {
                                contractMarkers();
                                // Tornar a mostrar el detector
                                clickDetector.setMap(mapInstance.map);

                                // Eliminar aquest listener fins la propera expansió
                                if (mapClickListener) {
                                    google.maps.event.removeListener(mapClickListener);
                                    mapClickListener = null;
                                }
                            }
                        }
                    });
                } else {
                    contractMarkers();
                    // Tornar a mostrar el detector
                    clickDetector.setMap(mapInstance.map);

                    // Eliminar listener si existeix
                    if (mapClickListener) {
                        google.maps.event.removeListener(mapClickListener);
                        mapClickListener = null;
                    }
                }
            });
        });
        FI FUNCIÓ ORIGINAL COMENTADA */
    },
    removeInvalidMosaicsButton: function() {
        // Eliminar el botó i el seu contenidor si existeixen
        var existingContainer = document.querySelector('.invalid-mosaics-button-container');
        if (existingContainer) {
            existingContainer.remove();
        }
    },
    addInvalidMosaicsButton: function() {
        // Verificar si l'usuari està registrat (només mostrar per usuaris registrats)
        if (!theia.user_logged_in) {
            return;
        }

        // Eliminar botó existent abans de crear-ne un de nou
        this.removeInvalidMosaicsButton();

        // Buscar el contenidor del mapa per afegir el botó sota
        var mapContainer = document.querySelector('.map__container, #map, .map-container');
        if (!mapContainer) {
            return;
        }

        // Crear contenidor per al botó sota el mapa
        var buttonContainer = document.createElement('div');
        buttonContainer.className = 'invalid-mosaics-button-container';
        buttonContainer.style.cssText = `
            text-align: center;
            margin-top: 15px;
            padding: 10px;
        `;

        var button = document.createElement('button');
        button.id = 'invalid-mosaics-button';
        button.className = 'invalid-mosaics-button';
        button.innerHTML = '🔍 Veure Mosaics amb Coordenades Invàlides';
        button.title = 'Veure llista completa de mosaics amb coordenades invàlides (només per usuaris registrats)';
        button.style.cssText = `
            background: #e74c3c;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 3px 6px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            display: inline-block;
        `;

        button.addEventListener('click', function() {
            // Obrir la pàgina PHP directament amb la URL correcta
            window.open('/arqueologiabarcelona/mosaics/mosaics-invalids/', '_blank');
        });

        button.addEventListener('mouseenter', function() {
            this.style.backgroundColor = '#c0392b';
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 5px 10px rgba(0,0,0,0.3)';
        });

        button.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '#e74c3c';
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 3px 6px rgba(0,0,0,0.2)';
        });

        buttonContainer.appendChild(button);

        // Afegir el contenidor sota el mapa
        mapContainer.parentNode.insertBefore(buttonContainer, mapContainer.nextSibling);
    },
    showInvalidMosaicsPanel: function() {
        // Crear la finestreta si no existeix
        if (!document.getElementById('invalid-mosaics-panel')) {
            var panel = document.createElement('div');
            panel.id = 'invalid-mosaics-panel';
            panel.className = 'invalid-mosaics-panel';
            panel.innerHTML = `
                <div class="invalid-mosaics-header">
                    <h3>🔍 Mosaics amb Coordenades Invàlides</h3>
                    <button class="invalid-mosaics-close" onclick="SN.map.hideInvalidMosaicsPanel()">×</button>
                </div>
                <div class="invalid-mosaics-content">
                    <div class="invalid-mosaics-loading">
                        <div class="map-spinner">
                            <div class="map-spinner-circle"></div>
                            <div class="map-spinner-text">0%</div>
                        </div>
                        Carregant mosaics invàlids...
                    </div>
                </div>
            `;
            document.body.appendChild(panel);
        }

        // Mostrar la finestreta
        document.getElementById('invalid-mosaics-panel').classList.add('show');

        // Carregar les dades
        this.loadInvalidMosaics();
    },
    hideInvalidMosaicsPanel: function() {
        var panel = document.getElementById('invalid-mosaics-panel');
        if (panel) {
            panel.classList.remove('show');
        }
    },
    loadInvalidMosaics: function() {
        console.log("🔍 Carregant mosaics invàlids reals...");

        // DEBUG COMPLET DE L'ESTAT
        console.log("🔍 window.lastMapResponse:", window.lastMapResponse);
        console.log("🔍 window.lastMapResponse.invalid_mosaics:", window.lastMapResponse ? window.lastMapResponse.invalid_mosaics : 'NO RESPONSE');
        console.log("🔍 Longitud invalid_mosaics:", window.lastMapResponse && window.lastMapResponse.invalid_mosaics ? window.lastMapResponse.invalid_mosaics.length : 'NO DATA');

        // Usar les dades reals del backend si estan disponibles a l'última resposta
        if (window.lastMapResponse && window.lastMapResponse.invalid_mosaics && window.lastMapResponse.invalid_mosaics.length > 0) {
            console.log("✅ Usant dades reals del backend");
            console.log("🔍 DADES REALS:", window.lastMapResponse.invalid_mosaics);

            var real_data = {
                invalid_count: window.lastMapResponse.invalid_mosaics.length,
                total_mosaics: window.lastMapResponse.count,
                invalid_mosaics: window.lastMapResponse.invalid_mosaics
            };

            console.log("📋 Mostrant " + real_data.invalid_count + " mosaics invàlids reals");
            this.displayInvalidMosaics(real_data);
        } else {
            console.log("⚠️ No hi ha dades reals disponibles, mostrant informació calculada");
            console.log("🔍 Raó: window.lastMapResponse =", !!window.lastMapResponse);
            console.log("🔍 Raó: invalid_mosaics =", window.lastMapResponse ? !!window.lastMapResponse.invalid_mosaics : 'NO RESPONSE');
            console.log("🔍 Raó: length > 0 =", window.lastMapResponse && window.lastMapResponse.invalid_mosaics ? window.lastMapResponse.invalid_mosaics.length > 0 : 'NO DATA');

            // Si no hi ha dades reals, mostrar informació basada en la diferència
            if (window.lastMapResponse && window.lastMapResponse.count && window.lastMapResponse.markers) {
                var excluded_count = window.lastMapResponse.count - window.lastMapResponse.markers.length;
                var calculated_data = {
                    invalid_count: excluded_count,
                    total_mosaics: window.lastMapResponse.count,
                    invalid_mosaics: []
                };

                // Mostrar informació genèrica si no tenim les dades reals
                for (var i = 0; i < excluded_count; i++) {
                    calculated_data.invalid_mosaics.push({
                        id: 'Desconegut',
                        title: 'Mosaic amb coordenades invàlides #' + (i + 1),
                        problem_type: 'Coordenades GPS incorrectes',
                        location: {lat: 'MISSING', lng: 'MISSING'},
                        edit_url: '/arqueologiabarcelona/mosaics/wp-admin/edit.php?post_type=mosaic'
                    });
                }

                console.log("📋 Mostrant " + excluded_count + " mosaics invàlids calculats");
                this.displayInvalidMosaics(calculated_data);
            } else {
                // Fallback complet
                console.log("📋 Mostrant missatge d'error");
                this.displayInvalidMosaicsError("No s'han pogut carregar les dades dels mosaics invàlids. Prova a aplicar un filtre al mapa primer.");
            }
        }
    },
    displayInvalidMosaics: function(data) {
        var content = document.querySelector('#invalid-mosaics-panel .invalid-mosaics-content');
        if (!content) return;

        if (data.invalid_count === 0) {
            content.innerHTML = `
                <div class="invalid-mosaics-summary">
                    ✅ Tots els ${data.total_mosaics} mosaics tenen coordenades vàlides
                </div>
            `;
            return;
        }

        var html = `
            <div class="invalid-mosaics-summary">
                ❌ ${data.invalid_count} de ${data.total_mosaics} mosaics tenen coordenades invàlides
            </div>
            <ul class="invalid-mosaics-list">
        `;

        for (var i = 0; i < data.invalid_mosaics.length; i++) {
            var mosaic = data.invalid_mosaics[i];
            var coords = 'Sense coordenades';

            if (mosaic.location && typeof mosaic.location === 'object') {
                var lat = mosaic.location.lat || 'MISSING';
                var lng = mosaic.location.lng || 'MISSING';
                coords = `lat: ${lat}, lng: ${lng}`;
            }

            html += `
                <li class="invalid-mosaic-item">
                    <div class="invalid-mosaic-title">${mosaic.title}</div>
                    <div class="invalid-mosaic-id">ID: ${mosaic.id}</div>
                    <div class="invalid-mosaic-problem">Problema: ${mosaic.problem_type}</div>
                    <div class="invalid-mosaic-coords">${coords}</div>
                    <a href="${mosaic.edit_url}" target="_blank" class="invalid-mosaic-edit">Editar Mosaic</a>
                </li>
            `;
        }

        html += '</ul>';
        content.innerHTML = html;
    },
    displayInvalidMosaicsError: function(customMessage) {
        var content = document.querySelector('#invalid-mosaics-panel .invalid-mosaics-content');
        if (!content) return;

        var message = customMessage || "Error carregant els mosaics invàlids. Prova de nou més tard.";

        content.innerHTML = `
            <div class="invalid-mosaics-error">
                ❌ ${message}
            </div>
        `;
    },
    getInvalidMosaicsDetails: function(expectedCount) {
        // Crida AJAX específica per obtenir detalls dels mosaics invàlids
        $.ajax({
            dataType: "json",
            type: "GET",
            url: theia.ajaxUrl,
            data: {
                action: 'theia__get_invalid_mosaics',
                // Passar els mateixos filtres que s'estan usant
                district: SN.map.ajaxCall.district || '',
                technique: SN.map.ajaxCall.technique || '',
                period: SN.map.ajaxCall.period || ''
            },
            success: function(response) {
                if (response.invalid_mosaics && response.invalid_mosaics.length > 0) {
                    console.warn("📋 DETALLS DELS MOSAICS INVÀLIDS:");
                    for (var i = 0; i < response.invalid_mosaics.length; i++) {
                        var mosaic = response.invalid_mosaics[i];
                        var locationInfo = "Coordenades invàlides";

                        if (mosaic.location) {
                            if (typeof mosaic.location === 'object') {
                                var lat = mosaic.location.lat || 'MISSING';
                                var lng = mosaic.location.lng || 'MISSING';
                                locationInfo = "lat: " + lat + ", lng: " + lng;
                            } else {
                                locationInfo = "No és un objecte vàlid";
                            }
                        }

                        console.warn("🔸 ID: " + mosaic.id + " | \"" + mosaic.title + "\" | " + locationInfo);
                    }
                } else {
                    console.warn("🔸 No s'han pogut obtenir els detalls específics dels mosaics invàlids");
                    console.warn("🔸 Consulta els logs del servidor per veure la informació completa");
                }
            },
            error: function() {
                console.warn("🔸 Error obtenint detalls dels mosaics invàlids");
                console.warn("🔸 Consulta els logs del servidor per veure la informació completa");
            }
        });
    },
    getItineraryMap: function () {
        var e = SN.map.itineraryMap.data("id");

        console.log('🗺️ Iniciant mapa d\'itinerari per ID:', e);

        // Verificar que els elements existeixen abans de crear els mapes
        var mobileElement = document.querySelector('.js__itinerary');
        var desktopElement = document.querySelector('.js__itinerary__desktop');

        console.log('🔍 Element mòbil trobat:', !!mobileElement);
        console.log('🔍 Element desktop trobat:', !!desktopElement);

        if (!mobileElement && !desktopElement) {
            console.error('🗺️ No s\'han trobat elements per al mapa d\'itinerari');
            return;
        }

        // NO crear GMaps per itineraris - només Maplace
        console.log('🗺️ Saltant creació de GMaps per itineraris - només usarem Maplace');

        var i = {
            map_div: ".js__itinerary",
            controls_on_map: !1,
            view_all_text: theia.itinerary_all,
            show_markers: !1,
            draggable: !1,
            type: "directions",
            map_options: {
                zoom: 14,
                center: [41.3818, 2.1685]
            }
        },
            n = {
                action: "theia__get_itinerary_markers",
                security: theia.ajaxNonce,
                itinerary_id: e
            };

        // Crear instàncies de Maplace només si els elements existeixen
        var t = null, a = null;

        if (mobileElement) {
            try {
                t = new Maplace(i);
                console.log('✅ Maplace mòbil creat');
            } catch (error) {
                console.error('❌ Error creant Maplace mòbil:', error);
            }
        }

        if (desktopElement) {
            try {
                i.map_div = ".js__itinerary__desktop";
                a = new Maplace(i);
                console.log('✅ Maplace desktop creat');
            } catch (error) {
                console.error('❌ Error creant Maplace desktop:', error);
            }
        }
        // Fer la petició AJAX per carregar les dades de l'itinerari
        $.ajax({
            dataType: "json",
            type: "GET",
            url: theia.ajaxUrl,
            data: ajaxData
        }).done(function (data) {
            console.log('📡 Dades d\'itinerari rebudes:', data);

            if (data && data.length > 0) {
                try {
                    maplace.Load({ locations: data });
                    console.log('✅ Mapa d\'itinerari carregat amb', data.length, 'ubicacions');
                } catch (error) {
                    console.error('❌ Error carregant mapa d\'itinerari:', error);
                }
            } else {
                console.info("🔍 No s'han trobat markers per aquest itinerari");
            }
        }).fail(function (xhr, status, error) {
            console.error("❌ Error AJAX:", xhr, status, error);
        });
    }
}, SN.inventoryGrid = {
    loading: null,
    init: function () {
        SN.cache.$body;
        var i = {
            action: "get_mosaics",
            security: theia.ajaxNonce,
            page: 1,
            postsNum: 12
        },
            c = SN.cache.$body.find(".js__inventory-grid"),
            n = c.find(".js__inventory-items");
        $allInventoryItems = c.find(".js__inventory-grid-item"), $inventoryCounter = SN.cache.$body.find(".js__filter__counter"), endedContent = !1, columnMode = 1, $loading = SN.cache.$body.find(".js__inventory__loading"), $loadButton = SN.cache.$body.find(".js__inventory-load-button"), $loadButtonContainer = SN.cache.$body.find(".inventory__load-button"), $filters = SN.cache.$body.find(".js__filters"), $fDistrict = $filters.find(".js__filter__district"), $fTechnique = $filters.find(".js__filter__technique"), $fPeriod = $filters.find(".js__filter__period"), $fKeyword = $filters.find(".js__filter__keyword"), $loading.hide();
        $loadingState = null;
        var t = function (r) {
            c.length && (c.find(".js__inventory-grid-block").each(function (e, i) {
                var n = $(this).find(".js__inventory-grid-item"),
                    t = r,
                    a = n.length,
                    o = 0,
                    s = 0;
                $(this).attr("data-block", e), 1 === r ? n.each(function (e, i) {
                    $(this).attr({
                        "data-row": e,
                        "data-row-item": e
                    })
                }) : (Math.ceil(a / t), n.each(function (e, i) {
                    $(this).attr({
                        "data-row": o,
                        "data-row-item": s
                    }), s < t - 1 ? s++ : (s = 0, o++)
                }))
            }), c.find(".inventory__item--has-subpanel").length && e())
        },
            a = function () {
                c.find(".inventory__panel").remove(), c.find(".inventory__item--has-subpanel").removeClass("inventory__item--has-subpanel")
            },
            e = function () {
                var e, i = $(".inventory__item--has-subpanel"),
                    n = i.parents(".js__inventory-grid-block").attr("data-block"),
                    t = i.attr("data-row"),
                    a = i.parent().find('[data-row="' + t + '"]').last().attr("data-row-item");
                e = 768 <= $(window).width() ? $('.js__inventory-grid-block[data-block="' + n + '"] .js__inventory-grid-item[data-row="' + t + '"][data-row-item="' + a + '"]') : $('.js__inventory-grid-block[data-block="' + n + '"] .js__inventory-grid-item[data-row="' + t + '"][data-row-item="' + t + '"]'), $(".js__inventory-panel").insertAfter(e)
            };

        {
            // Carregar comptes inicials de filtres per inventari
            SN.map.updateFilterCounts($fDistrict, $fTechnique, $fPeriod);

            // Inicialitzar gestió de canvis de vista també per inventari
            SN.map.initViewSwitcher();

            $filters.on("change", function (e) {
                e.preventDefault(), l($fDistrict.val(), $fTechnique.val(), $fPeriod.val(), $fKeyword.val()), SN.map.updateFilterCounts($fDistrict, $fTechnique, $fPeriod)
            });
            var o = function () {
                var e = c.find(".inventory__content:first").data("numposts");
                0 < e ? $inventoryCounter.html(e) : $inventoryCounter.html(0)
            },
                s = function () {
                    if ($loadingState) return;

                    $loadingState = true;
                    $.ajax({
                        type: "GET",
                        url: theia.ajaxUrl,
                        data: i,
                        beforeSend: function (e) {
                            // Mostrar spinner modern per inventari
                            $loading.fadeIn(300);
                            var spinner = $loading.find('.map-spinner')[0];
                            if (spinner && spinner.mapSpinnerInstance) {
                                spinner.mapSpinnerInstance.reset();
                                spinner.mapSpinnerInstance.setPercentage(10); // Inici de petició
                            }
                            endedContent = "loading";
                            o();
                        }
                    }).done(function (e) {
                        var spinner = $loading.find('.map-spinner')[0];
                        if (spinner && spinner.mapSpinnerInstance) {
                            spinner.mapSpinnerInstance.setPercentage(60); // Dades rebudes

                            setTimeout(function() {
                                if (0 == e) {
                                    // No hi ha més contingut
                                    endedContent = !0;
                                    spinner.mapSpinnerInstance.setPercentage(100);
                                    setTimeout(function() {
                                        spinner.mapSpinnerInstance.complete(function() {
                                            $loading.fadeOut(300, function() {
                                                if (0 == $(".inventory__item").length) {
                                                    n.append('<div class="inventory__no-mosaics">' + theia.inventory_list.no_mosaics + "</div>");
                                                }
                                                o();
                                            });
                                        });
                                    }, 200);
                                } else {
                                    // Afegir nou contingut
                                    spinner.mapSpinnerInstance.setPercentage(80); // Processant contingut
                                    setTimeout(function() {
                                        n.append(e);
                                        endedContent = !1;
                                        o();
                                        t(columnMode);
                                        i.page++;
                                        spinner.mapSpinnerInstance.setPercentage(100); // Contingut afegit
                                        setTimeout(function() {
                                            spinner.mapSpinnerInstance.complete(function() {
                                                $loading.fadeOut(300);
                                            });
                                        }, 200);
                                    }, 300);
                                }
                            }, 200);
                        } else {
                            // Fallback sense spinner
                            if (0 == e) {
                                endedContent = !0;
                                $loading.fadeOut("300", function () {
                                    if (0 == $(".inventory__item").length) {
                                        n.append('<div class="inventory__no-mosaics">' + theia.inventory_list.no_mosaics + "</div>");
                                    }
                                    o();
                                });
                            } else {
                                n.append(e);
                                endedContent = !1;
                                o();
                                t(columnMode);
                                i.page++;
                                $loading.fadeOut(300);
                            }
                        }

                        // Gestionar botons de càrrega
                        if (i.page - 1 >= c.find(".inventory__content:first").data("lastpage") || 1 == endedContent) {
                            $loadButtonContainer.hide();
                        } else {
                            $loadButtonContainer.show();
                        }
                    }).fail(function (e, i, n) {
                        console.error("jQuery XHR: " + e + "\nText Status: " + i + "\nError: " + n);

                        // Gestionar error en el spinner d'inventari
                        var spinner = $loading.find('.map-spinner')[0];
                        if (spinner && spinner.mapSpinnerInstance) {
                            spinner.mapSpinnerInstance.complete(function() {
                                $loading.fadeOut(300);
                            });
                        } else {
                            $loading.fadeOut(300);
                        }
                    }).always(function () {
                        $loadingState = false;
                    });
                };
            $loadButton.on("click", function () {
                s()
            });
            
            // Implementació de throttle per limitar les crides durant el scroll
            var scrollTimeout = null;
            var scrollThrottleTime = 200; // Temps en ms entre crides permeses
            
            $(window).on("scroll", function() {
                // Si ja hi ha un timeout pendent, no fer res
                if (scrollTimeout) return;
                
                // Establir un timeout per limitar les crides
                scrollTimeout = setTimeout(function() {
                    // Comprovar que n existeix i té contingut abans d'intentar accedir a offset
                    if (n && n.length && n.is(":visible")) {
                        var triggerAtY = $(n).offset().top - $(window).outerHeight() + $(n).height();
                        if ($(window).scrollTop() > triggerAtY) {
                            s();
                        }
                    }
                    
                    // Restablir el timeout a null per permetre noves crides
                    scrollTimeout = null;
                }, scrollThrottleTime);
            });
            
            var r = function (e) {
                e = e.split("#"), e = (e = atob(e[1])).split("-"), n.html(""), $loading.fadeIn(300), i.page = 1, 4 == e.length ? ($fDistrict.val(e[0]), $fTechnique.val(e[1]), $fPeriod.val(e[2]), $fKeyword.val(e[3]), i.district = e[0], i.technique = e[1], i.period = e[2], i.keyword = e[3]) : (console.error("Inventary problem: URL hash is'nt correct!"), $fDistrict.val(0), $fTechnique.val(0), $fPeriod.val(0), $fKeyword.val(""), i.district = 0, i.technique = 0, i.period = 0, i.keyword = ""), s()
            },
                l = function (e, i, n, t) {
                    var a = e + "-" + i + "-" + n + "-" + t;
                    a = btoa(a), window.location.hash = a
                };
            $(window).on("hashchange", function () {
                r(window.location.hash)
            });
            var d = window.location.hash;
            d ? r(d) : s()
        }
    }
}, SN.participate = {
    init: function () {
        var t = SN.cache.$body.find("#participate-form"),
            a = t.find(".js__form__loading"),
            o = (t.find(".js__form__accept"), t.find(".js__form__submit")),
            s = (t.find(".js__upload"), t.find(".js__upload__field")),
            r = t.find(".js__form__result"),
            e = t.find(".js__form-radio-replacement"),
            i = t.find(".js__form-checkbox-replacement"),
            l = SN.cache.$body.find(".js__thankyou-lightbox"),
            d = l.find(".js__thankyou-lightbox-close");
        if (t.length) {
            s.filer({
                fileMaxSize: 5,
                extensions: ["jpg", "jpeg", "png"],
                showThumbs: !0,
                captions: theia.participate.captions
            }), t.validate({
                submitHandler: function (e) {
                    var n = new FormData,
                        i = $(e).serializeArray();
                    s.length && $.each(s, function (e, i) {
                        $.each(i.files, function (e, i) {
                            n.append("files[" + e + "]", i)
                        })
                    }), $.each(i, function (e, i) {
                        n.append(i.name, i.value)
                    }), n.append("action", "process_form"), $.ajax({
                        type: "POST",
                        url: theia.ajaxUrl,
                        data: n,
                        dataType: "json",
                        contentType: !1,
                        processData: !1,
                        beforeSend: function () {
                            a.show().addClass("is-visible"), o.prop("disabled", "disabled")
                        }
                    }).done(function (e) {
                        a.removeClass("is-visible").hide(), o.removeProp("disabled"), e.errors ? (r.addClass("error"), $(e.errors).each(function (e, i) {
                            r.html("<li>" + i + "</li>")
                        })) : (l.show().addClass("js__thankyou-lightbox--visible"), d.on("click", function (e) {
                            e.preventDefault(), l.removeClass("js__thankyou-lightbox--visible").hide()
                        }), t.trigger("reset"), grecaptcha.reset(), s.prop("jFiler").reset())
                    }).fail(function (e) {
                        console.error(e), a.hide(), o.removeProp("disabled"), grecaptcha.reset(), s.prop("jFiler").reset()
                    })
                }
            });
            $.extend($.validator.messages, theia.participate.validate.messages)
        }
        var n = function () {
            e.each(function (e, i) {
                $(this).find("input").prop("checked") ? $(this).addClass("js__form-radio-replacement--is-checked") : $(this).removeClass("js__form-radio-replacement--is-checked")
            })
        };
        e.length && (n(), e.find("input").on("change", function (e) {
            n()
        }));
        var c = function () {
            i.each(function (e, i) {
                $(this).find("input").prop("checked") ? $(this).addClass("js__form-checkbox-replacement--is-checked") : $(this).removeClass("js__form-checkbox-replacement--is-checked")
            })
        };
        i.length && (c(), i.find("input").on("change", function (e) {
            c()
        }))
    }
}, SN.init = function () {
    SN.cache.init(), SN.externalLinks.init(), SN.cookies.init(), SN.sliders.init(), SN.mobileMenu.init(), SN.map.init(), SN.inventoryGrid.init(), SN.participate.init()
}, jQuery(function ($) {
    SN.init();
    var e = $(".grid").imagesLoaded(function () {
        e.masonry({
            itemSelector: ".grid-item",
            gutter: ".gutter-sizer",
            percentPosition: !0
        })
    });
    SN.cache.$body.width_snitch({
        style: {}
    })


    $(".mosaic__summary-item--image img").click(function () {
        $(".mosaic__summary-item--image").toggleClass("full");
    })
});

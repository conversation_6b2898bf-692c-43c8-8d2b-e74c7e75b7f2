/**
 * Leaflet GMaps Wrapper
 * Emula l'API de GMaps.js utilitzant Leaflet i OpenStreetMap
 * Manté compatibilitat amb el codi existent
 */

// Constructor principal que emula GMaps
function GMaps(options) {
    this.options = options;
    this.markers = [];
    this.markerCluster = null;
    this.overlappingMarkers = [];
    this.separatedMarkers = [];
    this.clickDetectors = [];
    this.initialized = false;

    // Inicialitzar el mapa amb retry si cal
    this.initWithRetry();
}

GMaps.prototype.initWithRetry = function() {
    var self = this;
    var maxRetries = 10;
    var retryCount = 0;

    function tryInit() {
        try {
            self.init();
            if (self.map) {
                self.initialized = true;
                console.log('🗺️ Mapa inicialitzat correctament:', self.options.div);
                return;
            }
        } catch (error) {
            console.warn('🗺️ Intent d\'inicialització fallit:', error.message);
        }

        retryCount++;
        if (retryCount < maxRetries) {
            setTimeout(tryInit, 100 * retryCount); // Retry amb backoff
        } else {
            console.error('🗺️ No s\'ha pogut inicialitzar el mapa després de', maxRetries, 'intents');
        }
    }

    // Primer intent immediat
    tryInit();
};

GMaps.prototype.init = function() {
    var self = this;

    // Obtenir l'element del DOM
    var mapElementId = this.options.div.replace('.', '').replace('#', '');
    var mapElement = document.getElementById(mapElementId);

    // Si no existeix l'element, buscar per classe
    if (!mapElement) {
        mapElement = document.querySelector(this.options.div);
    }

    // Verificar que l'element existeix
    if (!mapElement) {
        console.error('🗺️ Element del mapa no trobat:', this.options.div);
        return;
    }

    // Crear el mapa Leaflet
    this.map = L.map(mapElement, {
        center: [this.options.lat, this.options.lng],
        zoom: this.options.zoom || 13,
        zoomControl: this.options.zoomControl !== false,
        scrollWheelZoom: true
    });
    
    // Afegir capa vectorial en blanc i negre (CartoDB Positron)
    L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
        attribution: '© OpenStreetMap contributors © CARTO',
        subdomains: 'abcd',
        maxZoom: 20
    }).addTo(this.map);
    
    // Inicialitzar MarkerCluster si està disponible
    if (typeof L.markerClusterGroup !== 'undefined') {
        this.markerCluster = L.markerClusterGroup({
            maxClusterRadius: 50,
            spiderfyOnMaxZoom: false,
            spiderfyDistanceMultiplier: 2,
            showCoverageOnHover: false,
            zoomToBoundsOnClick: true,  // ACTIVAR zoom automàtic
            disableClusteringAtZoom: 16
        });
        this.map.addLayer(this.markerCluster);
        console.log('🗺️ MarkerCluster configurat amb zoom automàtic - SENSE desplegament');

        // Afegir event listener per detectar quan markers surten del cluster
        var self = this;
        this.markerCluster.on('animationend', function() {
            console.log('🔍 Cluster animation acabada - detectant markers sobreposats visibles');
            self.detectVisibleOverlappingMarkers();
        });
    }
    
    // Aplicar estils personalitzats (sempre - blanc i negre)
    this.applyCustomStyles();

    // Activar event listeners per reagrupar markers
    this.addOutsideClickListener();
};

// Emular addMarker de GMaps
GMaps.prototype.addMarker = function(markerOptions) {
    var self = this;

    // Verificar que el mapa està inicialitzat
    if (!this.map || !this.initialized) {
        console.warn('🗺️ Mapa no inicialitzat, guardant marker per afegir després');
        // Guardar marker per afegir quan el mapa estigui llest
        setTimeout(function() {
            self.addMarker(markerOptions);
        }, 200);
        return null;
    }

    // Crear icona personalitzada si s'especifica
    var icon = null;
    if (markerOptions.icon) {
        icon = L.icon({
            iconUrl: markerOptions.icon,
            iconSize: [25, 41],
            iconAnchor: [12, 41],
            popupAnchor: [1, -34]
        });
    }
    
    // Crear el marker
    var marker = L.marker([markerOptions.lat, markerOptions.lng], {
        icon: icon,
        title: markerOptions.title || ''
    });
    
    // Afegir infoWindow si existeix
    if (markerOptions.infoWindow && markerOptions.infoWindow.content) {
        marker.bindPopup(markerOptions.infoWindow.content, {
            maxWidth: 300,
            className: 'leaflet-popup-custom'
        });
    }
    
    // Afegir al cluster o directament al mapa
    if (this.markerCluster) {
        this.markerCluster.addLayer(marker);
        console.log('📎 Marker afegit al cluster - SENSE listeners automàtics');
    } else {
        marker.addTo(this.map);
        console.log('📍 Marker afegit directament al mapa - SENSE listeners automàtics');

        // Si s'afegeix directament, detectar sobreposicions immediatament
        var self = this;
        setTimeout(function() {
            self.detectVisibleOverlappingMarkers();
        }, 100);
    }

    // Guardar referència
    this.markers.push(marker);

    // Marcar que aquest marker pot necessitar separació
    marker._needsOverlapCheck = true;

    console.log('✅ Marker creat i afegit. Total markers:', this.markers.length);

    // Afegir event listeners si existeixen
    if (markerOptions.click) {
        marker.on('click', markerOptions.click);
    }

    // Afegir event listener per detectar sobreposicions en temps real
    var self = this;
    marker.on('click', function(e) {
        console.log('🖱️ Click sobre marker - verificant sobreposicions...');
        self.handleMarkerClick(marker, e);
    });
    
    return marker;
};

// Emular removeMarkers de GMaps
GMaps.prototype.removeMarkers = function() {
    var self = this;
    
    // Netejar markers del cluster
    if (this.markerCluster) {
        this.markerCluster.clearLayers();
    }
    
    // Eliminar markers individuals
    this.markers.forEach(function(marker) {
        if (self.map.hasLayer(marker)) {
            self.map.removeLayer(marker);
        }
    });
    
    // Netejar arrays
    this.markers = [];
    this.overlappingMarkers = [];
    this.separatedMarkers = [];
    
    // Eliminar click detectors
    this.clickDetectors.forEach(function(detector) {
        if (self.map.hasLayer(detector)) {
            self.map.removeLayer(detector);
        }
    });
    this.clickDetectors = [];
};

// Emular fitZoom de GMaps
GMaps.prototype.fitZoom = function() {
    if (this.markers.length > 0) {
        var group = new L.featureGroup(this.markers);
        this.map.fitBounds(group.getBounds(), {
            padding: [20, 20]
        });
        console.log('🗺️ Mapa reposicionat per mostrar', this.markers.length, 'markers');
    }
};

// Funció per ajustar bounds basant-se en coordenades específiques
GMaps.prototype.fitBoundsToMarkers = function(markersData) {
    if (!markersData || markersData.length === 0) {
        console.log('🗺️ No hi ha markers per ajustar bounds');
        return;
    }

    if (markersData.length === 1) {
        // Si només hi ha un marker, centrar-lo amb zoom adequat
        var marker = markersData[0];
        this.map.setView([marker.lat, marker.lng], 16);
        console.log('🗺️ Centrat en un sol marker:', marker.lat, marker.lng);
    } else {
        // Si hi ha múltiples markers, calcular bounds
        var bounds = L.latLngBounds();

        markersData.forEach(function(markerData) {
            if (markerData.lat && markerData.lng) {
                bounds.extend([markerData.lat, markerData.lng]);
            }
        });

        if (bounds.isValid()) {
            this.map.fitBounds(bounds, {
                padding: [30, 30],
                maxZoom: 16
            });
            console.log('🗺️ Bounds ajustats per', markersData.length, 'markers');
        }
    }
};

// Emular setCenter de GMaps
GMaps.prototype.setCenter = function(lat, lng) {
    this.map.setView([lat, lng]);
};

// Emular setZoom de GMaps
GMaps.prototype.setZoom = function(zoom) {
    this.map.setZoom(zoom);
};

// Emular addMarkers (plural) de GMaps
GMaps.prototype.addMarkers = function(markersArray) {
    var self = this;

    if (!markersArray || !Array.isArray(markersArray)) {
        console.warn('🗺️ addMarkers: Array de markers no vàlid');
        return;
    }

    console.log('🗺️ Afegint', markersArray.length, 'markers al mapa');

    markersArray.forEach(function(markerOptions) {
        self.addMarker(markerOptions);
    });

    console.log('🗺️ Total markers al mapa:', this.markers.length);

    // Ajustar bounds per mostrar tots els markers
    this.fitBoundsToMarkers(markersArray);

    // Detectar markers sobreposats visibles després d'afegir tots els markers
    var self = this;
    setTimeout(function() {
        self.detectVisibleOverlappingMarkers();
    }, 500); // Petit delay per assegurar que el clustering s'ha completat
};

// Funcions específiques per compatibilitat amb el codi existent
GMaps.prototype.addMarkersWithOverlapDetection = function(markersArray) {
    var self = this;

    // Afegir tots els markers
    this.addMarkers(markersArray);

    // Detectar sobreposicions
    var overlapping = this.detectOverlappingMarkers();

    // Afegir detectors per cada grup de markers sobreposats
    Object.keys(overlapping).forEach(function(key) {
        var coords = key.split(',');
        var lat = parseFloat(coords[0]);
        var lng = parseFloat(coords[1]);
        var markersGroup = overlapping[key];

        if (markersGroup.length > 1) {
            self.addOverlapDetector(lat, lng, markersGroup, function(markers) {
                self.separateOverlappingMarkers(markers, lat, lng);
            });
        }
    });
};

// Funció per detectar markers sobreposats NOMÉS quan són visibles (fora del cluster)
GMaps.prototype.detectVisibleOverlappingMarkers = function() {
    var self = this;

    console.log('🔍 Detectant markers sobreposats visibles...');

    // Obtenir tots els markers visibles (fora del cluster)
    var visibleMarkers = [];
    var totalLayers = 0;

    this.map.eachLayer(function(layer) {
        totalLayers++;
        console.log('🔍 Layer trobat:', layer.constructor.name, layer.options);

        if (layer instanceof L.Marker &&
            !layer._isClusterNode &&
            !layer.__parent &&
            !layer._childCount &&
            !(layer.options && layer.options.className && layer.options.className.includes('cluster')) &&
            layer.getLatLng &&
            !layer._isOverlapDetector &&
            !layer._isCenterMarker) {

            var pos = layer.getLatLng();
            console.log('👁️ Marker visible trobat a:', pos.lat.toFixed(6), pos.lng.toFixed(6));
            visibleMarkers.push(layer);
        }
    });

    console.log('📊 Total layers al mapa:', totalLayers);
    console.log('👁️ Markers visibles trobats:', visibleMarkers.length);

    if (visibleMarkers.length === 0) {
        console.log('⚠️ No hi ha markers visibles per processar');
        return;
    }

    // Agrupar per coordenades exactes
    var groups = {};
    visibleMarkers.forEach(function(marker) {
        var pos = marker.getLatLng();
        var key = pos.lat.toFixed(8) + ',' + pos.lng.toFixed(8);

        if (!groups[key]) {
            groups[key] = [];
        }
        groups[key].push(marker);
    });

    // Processar grups amb més d'un marker
    Object.keys(groups).forEach(function(key) {
        var markersGroup = groups[key];
        if (markersGroup.length > 1) {
            var coords = key.split(',');
            var lat = parseFloat(coords[0]);
            var lng = parseFloat(coords[1]);

            console.log('🎯 Detectats', markersGroup.length, 'markers visibles sobreposats a:', lat.toFixed(6), lng.toFixed(6));

            // Afegir event listeners per desplegament manual
            self.setupOverlapClickHandlers(markersGroup, lat, lng);
        }
    });
};

// Funció per configurar event handlers per markers sobreposats visibles
GMaps.prototype.setupOverlapClickHandlers = function(markers, centerLat, centerLng) {
    var self = this;

    console.log('🔧 Configurant handlers per', markers.length, 'markers sobreposats');

    markers.forEach(function(marker, index) {
        // Evitar afegir múltiples listeners
        if (marker._hasOverlapHandler) {
            return;
        }

        marker._hasOverlapHandler = true;
        marker._isOverlapping = true;
        marker._overlapGroup = markers;
        marker._originalPosition = [centerLat, centerLng];

        // Afegir indicador visual només al primer marker
        if (index === 0) {
            var popup = marker.getPopup();
            if (popup) {
                var content = popup.getContent();
                var newContent = content + '<br><small style="color: #ff6600;"><strong>📍 ' + markers.length + ' mosaics en aquesta ubicació - Click per separar</strong></small>';
                marker.setPopupContent(newContent);
            }
        }

        // Event listener per desplegament
        marker.on('click', function(e) {
            console.log('🖱️ Click sobre marker sobreposat visible');

            if (!marker._isSeparated) {
                console.log('🔄 Desplegant markers sobreposats...');

                // Aturar propagació
                L.DomEvent.stopPropagation(e);
                if (e.originalEvent) {
                    L.DomEvent.stop(e.originalEvent);
                }

                self.separateExactOverlappingMarkers(markers, centerLat, centerLng);
            } else {
                console.log('🎯 Click sobre marker separat');
                self._clickedOnSeparatedMarker = true;
                L.DomEvent.stopPropagation(e);
                if (e.originalEvent) {
                    L.DomEvent.stop(e.originalEvent);
                }
            }
        });
    });

    console.log('✅ Handlers configurats per markers sobreposats visibles');
};

// Funció de test manual per forçar detecció
GMaps.prototype.forceDetectOverlaps = function() {
    console.log('🧪 TEST: Forçant detecció de sobreposicions...');
    this.detectVisibleOverlappingMarkers();
};

// Funció per gestionar clicks sobre markers i detectar sobreposicions en temps real
GMaps.prototype.handleMarkerClick = function(clickedMarker, event) {
    var self = this;

    console.log('🔍 Verificant si hi ha markers sobreposats a la mateixa posició...');

    var clickedPos = clickedMarker.getLatLng();
    var overlappingMarkers = [clickedMarker];

    // Buscar altres markers a la mateixa posició exacta
    this.markers.forEach(function(marker) {
        if (marker !== clickedMarker && marker.getLatLng) {
            var markerPos = marker.getLatLng();

            // Comparar coordenades amb alta precisió
            if (Math.abs(markerPos.lat - clickedPos.lat) < 0.0000001 &&
                Math.abs(markerPos.lng - clickedPos.lng) < 0.0000001) {
                overlappingMarkers.push(marker);
                console.log('🎯 Marker sobreposat trobat a:', markerPos.lat.toFixed(8), markerPos.lng.toFixed(8));
            }
        }
    });

    console.log('📊 Total markers a la mateixa posició:', overlappingMarkers.length);

    if (overlappingMarkers.length > 1) {
        console.log('🔄 DESPLEGANT', overlappingMarkers.length, 'markers sobreposats en rotllana');

        // Aturar propagació per evitar comportament normal del popup
        L.DomEvent.stopPropagation(event);
        if (event.originalEvent) {
            L.DomEvent.stop(event.originalEvent);
        }

        // Desplegar en rotllana
        this.separateExactOverlappingMarkers(overlappingMarkers, clickedPos.lat, clickedPos.lng);
    } else {
        console.log('ℹ️ Marker únic - comportament normal (popup)');
        // Deixar que el comportament normal continuï
    }
};

// Funció per separar markers a la mateixa posició exacta (només quan es demana)
GMaps.prototype.separateExactOverlappingMarkers = function(markers, centerLat, centerLng) {
    var self = this;
    var radius = 0.0005; // Radi de separació
    var angleStep = (2 * Math.PI) / markers.length;

    // Verificar si ja estan separats
    if (markers[0]._isSeparated) {
        console.log('🔄 Markers ja separats, reagrupant...');
        this.regroupSeparatedMarkers(markers);
        return;
    }

    console.log('🔄 Separant', markers.length, 'markers sobreposats NOMÉS QUAN ES DEMANA');

    // PRIMER: Treure markers del cluster per poder-los moure lliurement
    var self = this;
    markers.forEach(function(marker) {
        if (self.markerCluster && self.markerCluster.hasLayer(marker)) {
            self.markerCluster.removeLayer(marker);
            console.log('📤 Marker tret del cluster');
        }
    });

    // NOMÉS CREAR marker central QUAN REALMENT ES SEPAREN
    var centerMarker = L.circleMarker([centerLat, centerLng], {
        radius: 8,
        fillColor: '#ff6600',
        color: '#ffffff',
        weight: 2,
        opacity: 1,
        fillOpacity: 0.8
    }).addTo(this.map);

    console.log('🎯 Marker central creat a:', centerLat, centerLng);

    // Afegir tooltip al marker central
    centerMarker.bindTooltip('Posició original (' + markers.length + ' mosaics)', {
        permanent: false,
        direction: 'top'
    });

    // Guardar referència als markers separats al marker central
    centerMarker._separatedMarkers = markers;

    // Afegir click listener al marker central per evitar reagrupament
    centerMarker.on('click', function(e) {
        console.log('🎯 Click sobre marker central - no reagrupar');
        console.log('📊 Markers associats al centre:', centerMarker._separatedMarkers.length);
        self._clickedOnSeparatedMarker = true;
        // Prevenir propagació del event amb Leaflet
        L.DomEvent.stopPropagation(e);
        if (e.originalEvent) {
            L.DomEvent.stop(e.originalEvent);
        }
    });

    markers.forEach(function(marker, index) {
        // Guardar posició original
        if (!marker._originalPosition) {
            marker._originalPosition = [centerLat, centerLng];
        }

        // Calcular nova posició en circumferència
        var angle = index * angleStep;
        var newLat = centerLat + (radius * Math.cos(angle));
        var newLng = centerLng + (radius * Math.sin(angle));

        // Moure marker a nova posició
        console.log('📍 Movent marker', index, 'de', centerLat, centerLng, 'a', newLat.toFixed(6), newLng.toFixed(6));
        marker.setLatLng([newLat, newLng]);

        // AFEGIR MARKER DIRECTAMENT AL MAPA (fora del cluster)
        if (!self.map.hasLayer(marker)) {
            marker.addTo(self.map);
            console.log('📍 Marker afegit directament al mapa');
        }

        // Crear línia de connexió a la posició original
        var connectionLine = L.polyline([
            [centerLat, centerLng],
            [newLat, newLng]
        ], {
            color: '#ff6600',
            weight: 2,
            opacity: 0.7,
            dashArray: '5, 5'
        }).addTo(self.map);

        // Guardar referències per poder eliminar-les després
        marker._connectionLine = connectionLine;
        marker._centerMarker = centerMarker;
        marker._isSeparated = true;

        // Modificar el popup per indicar que està desplaçat
        var originalPopup = marker.getPopup();
        if (originalPopup) {
            var content = originalPopup.getContent();
            var newContent = content + '<br><small style="color: #666;"><em>📍 Posició ajustada per visibilitat</em></small>';
            marker.setPopupContent(newContent);
        }
    });

    this.separatedMarkers = markers;
    console.log('✅ Markers separats en circumferència amb indicador central');
    console.log('📊 Markers separats guardats:', this.separatedMarkers.length);

    // FORÇAR ACTUALITZACIÓ DEL MAPA
    this.map.invalidateSize();

    // Configurar auto-reagrupament IMMEDIATAMENT
    this.setupAutoRegroup(markers);
};

// Aplicar estils personalitzats (simulació dels estils de Google Maps)
GMaps.prototype.applyCustomStyles = function() {
    // Els estils de Google Maps no es poden aplicar directament a OpenStreetMap
    // Però podem afegir CSS personalitzat per simular l'aparença
    var customCSS = `
        .leaflet-container {
            background-color: #ffffff;
        }
        /* Estils per mapa vectorial blanc i negre */
        .leaflet-tile-pane {
            filter: contrast(110%) brightness(95%);
        }
        .leaflet-popup-custom {
            font-family: Arial, sans-serif;
        }
        .leaflet-popup-custom .leaflet-popup-content {
            margin: 8px 12px;
            line-height: 1.4;
        }
        .leaflet-control-attribution {
            font-size: 10px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 3px;
        }
        /* Estils per markers de cluster taronja */
        .marker-cluster {
            background-color: #ff6600 !important;
            border: 2px solid #ffffff !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2) !important;
        }
        .marker-cluster div {
            background-color: #ff6600 !important;
            color: white !important;
            font-weight: bold !important;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.3) !important;
        }
        /* Estils per markers individuals */
        .leaflet-marker-icon {
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }
        /* Estils per popups */
        .leaflet-popup-content-wrapper {
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .leaflet-popup-tip {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        /* Estils per markers centrals de posició original */
        .leaflet-interactive[stroke="#ffffff"] {
            cursor: help;
        }
        /* Estils per línies de connexió */
        .leaflet-interactive[stroke="#ff6600"][stroke-dasharray] {
            pointer-events: none;
        }
    `;
    
    // Afegir CSS al head si no existeix
    if (!document.getElementById('leaflet-custom-styles')) {
        var style = document.createElement('style');
        style.id = 'leaflet-custom-styles';
        style.textContent = customCSS;
        document.head.appendChild(style);
    }
};

// Funcions addicionals per compatibilitat amb funcionalitats específiques del projecte

// Detectar markers sobreposats
GMaps.prototype.detectOverlappingMarkers = function() {
    var self = this;
    var overlapping = {};
    var threshold = 50; // Distància en metres per considerar sobreposició

    this.markers.forEach(function(marker1, i) {
        var pos1 = marker1.getLatLng();

        self.markers.forEach(function(marker2, j) {
            if (i !== j) {
                var pos2 = marker2.getLatLng();
                var distance = pos1.distanceTo(pos2);

                if (distance < threshold) {
                    var key = pos1.lat.toFixed(6) + ',' + pos1.lng.toFixed(6);
                    if (!overlapping[key]) {
                        overlapping[key] = [];
                    }
                    if (overlapping[key].indexOf(marker1) === -1) {
                        overlapping[key].push(marker1);
                    }
                    if (overlapping[key].indexOf(marker2) === -1) {
                        overlapping[key].push(marker2);
                    }
                }
            }
        });
    });

    return overlapping;
};

// Afegir detector de clicks per markers sobreposats
GMaps.prototype.addOverlapDetector = function(lat, lng, markers, callback) {
    var self = this;
    
    // Crear un marker invisible per detectar clicks
    var detector = L.marker([lat, lng], {
        opacity: 0,
        interactive: true,
        title: 'Click per separar ' + markers.length + ' markers sobreposats'
    });
    
    detector.on('click', function() {
        if (callback) {
            callback(markers);
        }
    });
    
    detector.addTo(this.map);
    this.clickDetectors.push(detector);
    
    return detector;
};

// Separar markers sobreposats
GMaps.prototype.separateOverlappingMarkers = function(markers, centerLat, centerLng) {
    var self = this;
    var radius = 0.005; // Radi MOLT més gran per veure's clarament (500 metres aprox)
    var angleStep = (2 * Math.PI) / markers.length;

    console.log('🎯 Separant', markers.length, 'markers sobreposats en radi');

    markers.forEach(function(marker, index) {
        var angle = index * angleStep;
        var newLat = centerLat + (radius * Math.cos(angle));
        var newLng = centerLng + (radius * Math.sin(angle));

        // Guardar posició original
        if (!marker._originalPosition) {
            marker._originalPosition = marker.getLatLng();
        }

        // Animar el moviment del marker
        marker.setLatLng([newLat, newLng]);

        // Afegir línia de connexió més visible
        var line = L.polyline([
            [centerLat, centerLng],
            [newLat, newLng]
        ], {
            color: '#ff6600',
            weight: 3,
            opacity: 0.8,
            dashArray: '3, 6'
        }).addTo(self.map);

        // Guardar referència per poder eliminar-la després
        marker._connectionLine = line;
        marker._isSeparated = true;
    });
    
    this.separatedMarkers = markers;
};

// Reagrupar markers separats
GMaps.prototype.regroupSeparatedMarkers = function(markers) {
    var self = this;

    // PRIMER: Eliminar event listeners per evitar bucles infinits
    this.removeAutoRegroupListeners();

    // Si no es passen markers, buscar-los de diferents maneres
    var markersToRegroup = markers || this.separatedMarkers || this.currentSeparatedMarkers;

    // Si encara no tenim markers, buscar tots els markers separats al mapa
    if (!markersToRegroup || markersToRegroup.length === 0) {
        console.log('🔍 Buscant markers separats al mapa...');
        markersToRegroup = [];

        this.markers.forEach(function(marker) {
            if (marker._isSeparated) {
                markersToRegroup.push(marker);
            }
        });

        console.log('📊 Markers separats trobats:', markersToRegroup.length);
    }

    console.log('🔄 Reagrupant', markersToRegroup ? markersToRegroup.length : 0, 'markers a la posició original');

    if (!markersToRegroup || markersToRegroup.length === 0) {
        console.warn('⚠️ No hi ha markers per reagrupar');
        return;
    }

    // Primer, eliminar tots els markers centrals
    var centerMarkersRemoved = 0;
    markersToRegroup.forEach(function(marker) {
        if (marker._centerMarker) {
            self.map.removeLayer(marker._centerMarker);
            delete marker._centerMarker;
            centerMarkersRemoved++;
        }
    });
    console.log('🗑️ Markers centrals eliminats:', centerMarkersRemoved);

    // Després, processar cada marker
    markersToRegroup.forEach(function(marker) {
        // Eliminar línia de connexió
        if (marker._connectionLine) {
            self.map.removeLayer(marker._connectionLine);
            delete marker._connectionLine;
        }

        // Treure marker del mapa directe
        if (self.map.hasLayer(marker)) {
            self.map.removeLayer(marker);
            console.log('📤 Marker tret del mapa directe');
        }

        // Tornar a posició original
        if (marker._originalPosition) {
            marker.setLatLng(marker._originalPosition);
            console.log('📍 Marker tornat a posició original:', marker._originalPosition);
        }

        // TORNAR MARKER AL CLUSTER
        if (self.markerCluster) {
            self.markerCluster.addLayer(marker);
            console.log('📥 Marker tornat al cluster');
        }

        // Marcar com no separat
        marker._isSeparated = false;

        // Restaurar popup original
        var popup = marker.getPopup();
        if (popup) {
            var content = popup.getContent();
            if (content.includes('📍 Posició ajustada per visibilitat')) {
                var cleanContent = content.replace(/<br><small[^>]*>.*?<\/small>/g, '');
                marker.setPopupContent(cleanContent);
            }
        }
    });

    this.separatedMarkers = [];

    // Eliminar listeners d'auto-reagrupament si existeixen
    this.removeAutoRegroupListeners();

    console.log('✅ Markers reagrupats a la posició original');
};

// Funció alternativa per reagrupar TOTS els markers separats
GMaps.prototype.regroupAllSeparatedMarkers = function() {
    var self = this;
    var separatedMarkers = [];
    var centerMarkers = [];

    console.log('🔍 Buscant TOTS els markers separats i centrals...');

    // Buscar tots els markers separats
    this.markers.forEach(function(marker) {
        if (marker._isSeparated) {
            separatedMarkers.push(marker);
        }
    });

    // Buscar tots els markers centrals (poden estar en una llista separada)
    this.map.eachLayer(function(layer) {
        if (layer._separatedMarkers) {
            centerMarkers.push(layer);
        }
    });

    console.log('📊 Trobats:', separatedMarkers.length, 'markers separats i', centerMarkers.length, 'markers centrals');

    // Eliminar markers centrals
    centerMarkers.forEach(function(centerMarker) {
        self.map.removeLayer(centerMarker);
    });

    // Reagrupar markers separats
    separatedMarkers.forEach(function(marker) {
        // Eliminar línia de connexió
        if (marker._connectionLine) {
            self.map.removeLayer(marker._connectionLine);
            delete marker._connectionLine;
        }

        // Treure del mapa directe
        if (self.map.hasLayer(marker)) {
            self.map.removeLayer(marker);
        }

        // Tornar a posició original
        if (marker._originalPosition) {
            marker.setLatLng(marker._originalPosition);
        }

        // Tornar al cluster
        if (self.markerCluster) {
            self.markerCluster.addLayer(marker);
        }

        // Marcar com no separat
        marker._isSeparated = false;

        // Netejar referències
        delete marker._centerMarker;
        delete marker._originalPosition;
    });

    // Netejar referències globals
    this.separatedMarkers = [];
    this.currentSeparatedMarkers = [];

    console.log('✅ Tots els markers reagrupats forçadament');
};

// Funció de test per forçar desplegament manual
GMaps.prototype.testSeparateMarkers = function() {
    console.log('🧪 TEST: Forçant separació de markers...');

    // Buscar el primer grup de markers a la mateixa posició
    var groups = {};

    this.markers.forEach(function(marker) {
        var pos = marker.getLatLng();
        var key = pos.lat.toFixed(8) + ',' + pos.lng.toFixed(8);

        if (!groups[key]) {
            groups[key] = [];
        }
        groups[key].push(marker);
    });

    // Trobar el primer grup amb més d'un marker
    var firstGroup = null;
    var firstKey = null;

    Object.keys(groups).forEach(function(key) {
        if (!firstGroup && groups[key].length > 1) {
            firstGroup = groups[key];
            firstKey = key;
        }
    });

    if (firstGroup) {
        var coords = firstKey.split(',');
        var lat = parseFloat(coords[0]);
        var lng = parseFloat(coords[1]);

        console.log('🧪 TEST: Separant', firstGroup.length, 'markers a', lat, lng);
        this.separateExactOverlappingMarkers(firstGroup, lat, lng);
    } else {
        console.log('🧪 TEST: No s\'han trobat markers per separar');
    }
};

// Configurar auto-reagrupament per markers separats
GMaps.prototype.setupAutoRegroup = function(separatedMarkers) {
    var self = this;

    console.log('🔧 Configurant auto-reagrupament per', separatedMarkers.length, 'markers');

    // Guardar referència dels markers separats
    this.currentSeparatedMarkers = separatedMarkers;

    // 1. Event listener per clicks fora del grup de markers
    var mapClickHandler = function(event) {
        console.log('🖱️ Click detectat al mapa');

        // Verificar si el click va ser sobre el marker central o línies
        var clickedOnGroupElement = false;

        // Verificar marker central
        if (separatedMarkers[0]._centerMarker) {
            var centerElement = separatedMarkers[0]._centerMarker.getElement();
            if (centerElement && event.originalEvent && centerElement.contains(event.originalEvent.target)) {
                clickedOnGroupElement = true;
                console.log('🎯 Click sobre marker central - no reagrupar');
            }
        }

        if (!clickedOnGroupElement && !self._clickedOnSeparatedMarker) {
            console.log('👆 Click fora del grup - reagrupant markers');
            console.log('📊 Markers a reagrupar:', separatedMarkers.length);

            // Provar primer la funció normal, després l'alternativa
            if (separatedMarkers && separatedMarkers.length > 0) {
                self.regroupSeparatedMarkers(separatedMarkers);
            } else {
                console.log('🔄 Usant funció alternativa de reagrupament...');
                self.regroupAllSeparatedMarkers();
            }
        }

        // Reset flag
        self._clickedOnSeparatedMarker = false;
    };

    // 2. Event listener per clicks sobre altres markers
    var markerClickHandler = function(event) {
        var clickedMarker = event.target;
        var isPartOfSeparatedGroup = separatedMarkers.includes(clickedMarker);

        if (!isPartOfSeparatedGroup) {
            console.log('👆 Click sobre altre marker - reagrupant markers');
            console.log('📊 Markers a reagrupar:', separatedMarkers.length);

            if (separatedMarkers && separatedMarkers.length > 0) {
                self.regroupSeparatedMarkers(separatedMarkers);
            } else {
                self.regroupAllSeparatedMarkers();
            }
            self.removeAutoRegroupListeners();
        }
    };

    // 3. Event listener per canvis de zoom
    var zoomStartHandler = function(event) {
        console.log('🔍 Zoom iniciat - reagrupant markers');
        console.log('📊 Markers a reagrupar:', separatedMarkers.length);
        self.regroupSeparatedMarkers(separatedMarkers);
    };

    var zoomEndHandler = function(event) {
        console.log('🔍 Zoom finalitzat - reagrupant markers');
        console.log('📊 Markers a reagrupar:', separatedMarkers.length);
        self.regroupSeparatedMarkers(separatedMarkers);
    };

    // 4. Event listener per moviment del mapa (DESACTIVAT per evitar bucles infinits)
    var moveStartHandler = function(event) {
        console.log('🗺️ Moviment del mapa iniciat - reagrupament desactivat per evitar bucles');
        // COMENTAT per evitar bucles infinits amb setLatLng
        // self.regroupSeparatedMarkers(separatedMarkers);
    };

    var moveEndHandler = function(event) {
        console.log('🗺️ Moviment del mapa finalitzat');
        // Opcional: també reagrupar al final del moviment
        // self.regroupSeparatedMarkers(separatedMarkers);
    };

    // Afegir event listeners amb logs de confirmació
    console.log('📎 Afegint event listeners...');

    this.map.on('click', mapClickHandler);
    console.log('✅ Listener de click afegit');

    this.map.on('zoomstart', zoomStartHandler);
    this.map.on('zoomend', zoomEndHandler);
    console.log('✅ Listeners de zoom afegits');

    // COMENTAT: Listeners de moviment causen bucles infinits
    // this.map.on('movestart', moveStartHandler);
    // this.map.on('moveend', moveEndHandler);
    console.log('⚠️ Listeners de moviment desactivats per evitar bucles');

    // Afegir listeners a tots els altres markers del mapa
    var otherMarkersCount = 0;
    this.markers.forEach(function(marker) {
        if (!separatedMarkers.includes(marker)) {
            marker.on('click', markerClickHandler);
            otherMarkersCount++;
        }
    });
    console.log('✅ Listeners afegits a', otherMarkersCount, 'altres markers');

    // Guardar referències per poder eliminar-les després
    this._autoRegroupListeners = {
        mapClick: mapClickHandler,
        markerClick: markerClickHandler,
        zoomStart: zoomStartHandler,
        zoomEnd: zoomEndHandler,
        moveStart: moveStartHandler,
        moveEnd: moveEndHandler,
        separatedMarkers: separatedMarkers
    };

    console.log('✅ Auto-reagrupament configurat');
};

// Eliminar event listeners d'auto-reagrupament
GMaps.prototype.removeAutoRegroupListeners = function() {
    if (this._autoRegroupListeners) {
        console.log('🧹 Eliminant listeners d\'auto-reagrupament');

        // Eliminar listeners del mapa
        this.map.off('click', this._autoRegroupListeners.mapClick);
        this.map.off('zoomstart', this._autoRegroupListeners.zoomStart);
        this.map.off('zoomend', this._autoRegroupListeners.zoomEnd);
        // Listeners de moviment ja no s'afegeixen
        // this.map.off('movestart', this._autoRegroupListeners.moveStart);
        // this.map.off('moveend', this._autoRegroupListeners.moveEnd);

        // Eliminar listeners dels markers
        var self = this;
        this.markers.forEach(function(marker) {
            if (!self._autoRegroupListeners.separatedMarkers.includes(marker)) {
                marker.off('click', self._autoRegroupListeners.markerClick);
            }
        });

        // Netejar referències
        delete this._autoRegroupListeners;
        delete this.currentSeparatedMarkers;
        delete this._lastMapCenter;
        delete this._clickedOnSeparatedMarker;

        console.log('✅ Listeners eliminats');
    }
};

// Afegir event listener per reagrupar markers quan es fa click fora
GMaps.prototype.addOutsideClickListener = function() {
    var self = this;

    // Event listener per clicks fora del mapa
    document.addEventListener('click', function(event) {
        var mapContainer = self.map.getContainer();
        if (!mapContainer.contains(event.target)) {
            // Click fora del mapa - reagrupar markers separats
            self.regroupSeparatedMarkers();
        }
    });

    // Event listener per clicks dins del mapa (però no sobre markers)
    this.map.on('click', function(event) {
        // Si no s'ha fet click sobre un marker, reagrupar
        var clickedOnMarker = false;
        self.markers.forEach(function(marker) {
            if (marker.getLatLng().distanceTo(event.latlng) < 0.001) {
                clickedOnMarker = true;
            }
        });

        if (!clickedOnMarker) {
            self.regroupSeparatedMarkers();
        }
    });
};

// Compatibilitat amb google.maps namespace (si es necessita)
if (typeof google === 'undefined') {
    window.google = {
        maps: {
            Marker: function(options) {
                // Wrapper bàsic per compatibilitat
                return L.marker([options.position.lat, options.position.lng]);
            },
            Size: function(width, height) {
                return { width: width, height: height };
            },
            Point: function(x, y) {
                return { x: x, y: y };
            },
            TravelMode: {
                WALKING: 'walking',
                DRIVING: 'driving',
                BICYCLING: 'bicycling',
                TRANSIT: 'transit'
            }
        }
    };
}

// Compatibilitat amb Maplace (per itineraris)
if (typeof Maplace === 'undefined') {
    window.Maplace = function(options) {
        this.options = options;
        this.map = null;
        this.markers = [];
        this.routes = [];
        this.currentTransport = 'foot-walking'; // Transport per defecte

        this.Load = function(data) {
            var self = this; // Definir self al començament
            var mapDiv = this.options.map_div.replace('.', '').replace('#', '');

            console.log('🗺️ Maplace.Load: Buscant element del mapa:', mapDiv);

            // Verificar que l'element existeix
            var mapElement = document.getElementById(mapDiv);
            if (!mapElement) {
                mapElement = document.querySelector(this.options.map_div);
            }

            if (!mapElement) {
                console.error('🗺️ Maplace: Element del mapa no trobat:', this.options.map_div);
                return;
            }

            console.log('🗺️ Maplace: Element trobat:', mapElement);

            // Netejar l'element si ja té un mapa inicialitzat
            if (mapElement._leaflet_id) {
                console.log('🧹 Netejant mapa existent de l\'element');
                mapElement._leaflet_id = null;
                mapElement.innerHTML = '';
            }

            // Crear mapa si no existeix
            if (!this.map) {
                try {
                    this.map = L.map(mapElement, {
                        center: [41.3818, 2.1685],
                        zoom: 14,
                        zoomControl: true
                    });

                    L.tileLayer('https://{s}.basemaps.cartocdn.com/light_all/{z}/{x}/{y}{r}.png', {
                        attribution: '© OpenStreetMap contributors © CARTO',
                        subdomains: 'abcd',
                        maxZoom: 20
                    }).addTo(this.map);

                    console.log('🗺️ Maplace: Mapa d\'itinerari creat correctament');
                } catch (error) {
                    console.error('🗺️ Maplace: Error creant mapa:', error);
                    return;
                }
            }

            // Debug: Mostrar dades rebudes
            console.log('🗺️ Maplace: Dades rebudes:', data);

            // Afegir markers si hi ha ubicacions
            if (data.locations && data.locations.length > 0) {
                console.log('🗺️ Maplace: Processant', data.locations.length, 'ubicacions per itinerari');
                console.log('🗺️ Maplace: Primera ubicació:', data.locations[0]);

                var bounds = L.latLngBounds();
                var waypoints = [];

                // Configurar event listeners per canvis de transport
                setupTransportControls();

                data.locations.forEach(function(location, index) {
                    // Validar coordenades
                    if (!location.lat || !location.lng ||
                        isNaN(location.lat) || isNaN(location.lng)) {
                        console.warn('🗺️ Coordenades invàlides per ubicació', index, location);
                        return;
                    }

                    console.log('📍 Creant marker a:', location.lat, location.lng);

                    var marker = L.marker([parseFloat(location.lat), parseFloat(location.lng)]);

                    if (location.html) {
                        marker.bindPopup(location.html);
                    }

                    marker.addTo(self.map);
                    self.markers.push(marker);
                    bounds.extend([parseFloat(location.lat), parseFloat(location.lng)]);
                    waypoints.push(L.latLng(parseFloat(location.lat), parseFloat(location.lng)));
                });

                // Ajustar vista per mostrar tots els markers
                if (bounds.isValid()) {
                    self.map.fitBounds(bounds, { padding: [20, 20] });
                }

                // Guardar waypoints per rutes
                this.waypoints = waypoints;

                // Calcular ruta inicial si hi ha més d'un punt
                if (waypoints.length > 1 && self.options.type === 'directions') {
                    calculateRoute(waypoints, self.currentTransport);
                }
            }

            // Funcions locals per gestionar transport i rutes
            function setupTransportControls() {
                var mapDiv = self.options.map_div;
                var mapElement = document.querySelector(mapDiv);
                var transportControls = null;

                if (mapElement && mapElement.parentElement) {
                    transportControls = mapElement.parentElement.querySelector('.itinerary-transport-controls');
                }

                if (transportControls) {
                    var radioButtons = transportControls.querySelectorAll('input[type="radio"]');

                    radioButtons.forEach(function(radio) {
                        radio.addEventListener('change', function() {
                            if (this.checked) {
                                console.log('🚗 Canviant transport a:', this.value);
                                self.currentTransport = this.value;

                                // Recalcular ruta amb nou transport
                                if (self.waypoints && self.waypoints.length > 1) {
                                    calculateRoute(self.waypoints, self.currentTransport);
                                }
                            }
                        });
                    });

                    console.log('✅ Controls de transport configurats');
                } else {
                    console.warn('⚠️ No s\'han trobat controls de transport per:', mapDiv);
                }
            }

            function calculateRoute(waypoints, transport) {
                console.log('🗺️ Calculant ruta amb transport:', transport);

                // Eliminar rutes anteriors
                if (self.currentRoute) {
                    self.map.removeLayer(self.currentRoute);
                }

                // Preparar coordenades per OSRM
                var coordinates = waypoints.map(function(point) {
                    return [point.lng, point.lat]; // OSRM usa [lng, lat]
                });

                // Configurar perfil de transport
                var profile = getOSRMProfile(transport);

                // Calcular ruta amb OSRM
                calculateOSRMRoute(coordinates, profile);
            }

            function getOSRMProfile(transport) {
                switch(transport) {
                    case 'driving-car':
                        return 'driving';
                    case 'cycling-regular':
                        return 'cycling';
                    case 'foot-walking':
                    default:
                        return 'walking';
                }
            }

            function calculateOSRMRoute(coordinates, profile) {
                // Construir URL per OSRM
                var coordsString = coordinates.map(function(coord) {
                    return coord[0] + ',' + coord[1];
                }).join(';');

                var url = 'https://router.project-osrm.org/route/v1/' + profile + '/' + coordsString +
                         '?overview=full&geometries=geojson&steps=true';

                console.log('🌐 Petició OSRM:', url);

                fetch(url)
                    .then(function(response) {
                        if (!response.ok) {
                            throw new Error('Error en la resposta OSRM: ' + response.status);
                        }
                        return response.json();
                    })
                    .then(function(data) {
                        if (data.routes && data.routes.length > 0) {
                            displayRoute(data.routes[0], profile);
                        } else {
                            console.warn('⚠️ No s\'ha trobat ruta');
                            drawStraightLines();
                        }
                    })
                    .catch(function(error) {
                        console.error('❌ Error calculant ruta:', error);
                        drawStraightLines();
                    });
            }

            function displayRoute(route, profile) {
                // Color segons transport
                var color = getRouteColor(profile);

                // Crear línia de la ruta
                var routeLine = L.geoJSON(route.geometry, {
                    style: {
                        color: color,
                        weight: 4,
                        opacity: 0.8
                    }
                }).addTo(self.map);

                self.currentRoute = routeLine;
                self.routes.push(routeLine);

                // Mostrar informació de la ruta
                var distance = (route.distance / 1000).toFixed(1); // km
                var duration = Math.round(route.duration / 60); // minuts

                console.log('✅ Ruta calculada:', distance + 'km,', duration + 'min');
            }

            function getRouteColor(profile) {
                switch(profile) {
                    case 'driving':
                        return '#2196F3'; // Blau per cotxe
                    case 'cycling':
                        return '#4CAF50'; // Verd per bici
                    case 'walking':
                    default:
                        return '#FF6600'; // Taronja per caminar
                }
            }

            function drawStraightLines() {
                console.log('📏 Dibuixant línies rectes entre punts');

                if (self.currentRoute) {
                    self.map.removeLayer(self.currentRoute);
                }

                if (self.waypoints && self.waypoints.length > 1) {
                    var polyline = L.polyline(self.waypoints, {
                        color: '#FF6600',
                        weight: 3,
                        opacity: 0.6,
                        dashArray: '10, 10'
                    }).addTo(self.map);

                    self.currentRoute = polyline;
                    self.routes.push(polyline);
                }
            }
        };


        return this;
    };
}

// Verificar que les dependències estan disponibles
if (typeof L === 'undefined') {
    console.error('🗺️ Leaflet no està carregat! Assegura\'t que Leaflet s\'ha carregat abans del wrapper.');

    // Crear un GMaps dummy per evitar errors
    window.GMaps = function() {
        console.warn('🗺️ GMaps wrapper no disponible - Leaflet no carregat');
        return {
            addMarker: function() { console.warn('🗺️ addMarker no disponible'); },
            removeMarkers: function() { console.warn('🗺️ removeMarkers no disponible'); },
            fitZoom: function() { console.warn('🗺️ fitZoom no disponible'); }
        };
    };
} else {
    console.log('🗺️ Leaflet GMaps Wrapper carregat correctament - OpenStreetMap actiu!');
    console.log('🗺️ Versió Leaflet:', L.version);
    console.log('🗺️ Logs forçats a mostrar-se a la consola');
}

// Protegir contra conflictes amb altres llibreries
(function() {
    'use strict';

    // Verificar que jQuery està disponible
    if (typeof jQuery === 'undefined' && typeof $ === 'undefined') {
        console.warn('🗺️ jQuery no detectat. Algunes funcionalitats poden no funcionar.');
    }

    // Verificar que no hi ha conflictes amb altres mapes
    if (typeof google !== 'undefined' && google.maps && google.maps.Map) {
        console.warn('🗺️ Google Maps API detectada. Pot haver conflictes amb OpenStreetMap.');
    }
})();

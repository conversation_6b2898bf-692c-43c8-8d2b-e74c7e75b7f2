@charset "UTF-8";





@font-face {

    font-family: 'dsignes_boldregular';

    src: url('fonts/dsignesbold-webfont.eot');

    src: url('fonts/dsignesbold-webfont.eot?#iefix') format('embedded-opentype'),

         url('fonts/dsignesbold-webfont.woff') format('woff'),

         url('fonts/dsignesbold-webfont.ttf') format('truetype'),

         url('fonts/dsignesbold-webfont.svg#dsignes_boldregular') format('svg');

    font-weight: normal;

    font-style: normal;



}





@font-face {

    font-family:'dsignes_lightregular';

    src: url('fonts/dsigneslight-webfont.eot');

    src: url('fonts/dsigneslight-webfont.eot?#iefix') format('embedded-opentype'),

         url('fonts/dsigneslight-webfont.woff') format('woff'),

         url('fonts/dsigneslight-webfont.ttf') format('truetype'),

         url('fonts/dsigneslight-webfont.svg#dsignes_lightregular') format('svg');

    font-weight: normal;

    font-style: normal;



}









@font-face {

    font-family: 'dsignes_regularregular';

    src: url('fonts/dsignesregular-webfont.eot');

    src: url('fonts/dsignesregular-webfont.eot?#iefix') format('embedded-opentype'),

         url('fonts/dsignesregular-webfont.woff') format('woff'),

         url('fonts/dsignesregular-webfont.ttf') format('truetype'),

         url('fonts/dsignesregular-webfont.svg#dsignes_regularregular') format('svg');

    font-weight: normal;

    font-style: normal;



}









#brand-cultura.v2012.white {

    background-color: #fff;

    filter: invert(1);

}





#home-slide-16327 .home-slide__cover {

    background-size: contain;

    background-color: #f0486e;

}

#home-slide-16327 .home-slide__details {

    display:none;

}

.autor-imatge {

    position:absolute;

    bottom: 0px;

    margin:0px;

    right:0px;

    background-color:rgba(0,0,0,0.2);

    padding:5px;

    padding-left:15px;

    padding-right:15px;

    font-size:12px;

    color:rgba(255,255,255,1);

}



.accordion-title {

	clear:both;

	border-top:1px dotted rgba(0,0,0,0.5);

	padding-top:20px;

	margin:0px!important;

}

hr {

	clear:both;

	opacity:0;

	margin:0px;

}

#brand .wrapper {

    max-width: 100%;

}

.no-margin {

	margin-left:0px!important; 

	margin-right:0px!important;

}



	.cms__content--page-static h4{

		margin-top: 1.25rem;

		color:#ff6600;

		

		

	}

@media screen and (min-width: 1024px) {

    #brand.v2015.v2015-ajuntament-f.restyle header.navbar .wrapper.legacy .content {

        width:auto;

		padding-left:20px;

        

    }

}

div.snitch {

	display:none;

}





@media all {

    .site-header {

		position:relative;

		z-index:10000;

    }

}

	

   

.inventory__details a {

	text-decoration:none;

}

.inventory__details a:hover {

	text-decoration:underline;

}

.inventory__detail-item {

	    font-size: 14px;

    margin-bottom: 0.3em;

    font-weight:300;

}

.inventory__detail-item:nth-child(1) {

	    font-size: 1.15rem;

    line-height: 120%;

    margin-bottom: 0.8em;

    font-weight: 500;

}

/* HEADER */



.site-header__main {

    background-color: rgba(255, 255, 255, 0);

}

.page .site-header__main, .single .site-header__main {

    background-color: #fff;

    position:relative;

}

.site-header__area {

    font-family: "dsignes_regularregular", sans-serif;

}

.home .site-header__area {

    font-family: "dsignes_regularregular", sans-serif;

    word-spacing: -1.5px;

	 background-color:#fff;

			padding-top:6px;

			padding-bottom:6px;

           

    border-radius: 6px;

            display:inline-block;

   

}

	

	.site-header--home .site-header__main-title {

    font-size: 2rem;

    word-spacing: -1.5px;

    line-height: 102%;

    padding-right: 0px;

    box-sizing: border-box;

    color: #000;



    }



    .home-slide__details {     



        padding: 10px;

        padding-left:0;

        box-sizing: border-box;

    }



    .menu-toggle {

        top: -50px;

    }

    .site-header--home .site-header__area {

        font-size: 1.14rem; 

margin-top:4px;

    }



.featured-conference__cta .button {

    display: block;

   

    box-sizing: border-box;

    width:100%;

}

.button--take-part .button__label {

    line-height: 150%;

    font-size: 1rem;

    text-align: left;

}







.site-header__branding .sotstitol{

	line-height:normal;

	margin:0;

	max-width:90%;

        margin-top: 0.3em;

	color:rgba(0,0,0,1.00);

	display:inline-block;

	font-weight:900;

	font-size:1rem;

}



@media screen and (min-width: 375px) {   

    .menu-toggle {

        top: -30px;

    }

}

@media screen and (min-width: 421px){

    .menu-toggle {

        top: -13px;

    }

}

@media screen and (min-width: 445px){

    .menu-toggle {

        top: 10px;

    }

}





@media screen and (min-width: 600px){

    .featured-conference__stamp {

        margin-top: 2.7875rem;

        padding-right: 22px;

    }

    .featured-conference__stamp, .featured-conference__video{

        width:50%;

        box-sizing: border-box;

        float:left;

    }

}



@media screen and (min-width: 768px){

    .menu-toggle {

        margin-top: -7rem;

    }

    .featured-conference__video {

        padding-top: 2.7875rem;

    }

}

@media screen and (min-width: 1024px) {   

.menu-toggle {

    top: -151px;

}

    

    .featured-conference__stamp, .featured-conference__video{

      

    }

    .featured-conference__stamp {

        margin-top: 50px;

        padding-right: 11px;

        padding-left: 11px;

    }

    .featured-conference__video {

        padding-top:0px;

        text-align:right;

       

    }

    .featured-conference__video iframe {

         height:calc(100% - 4rem)!important;

        width: 100%;

        margin-top:2rem;

    }

    .featured-conference__cta .button {

   

        width: auto;

    }

}



@media screen and (min-width: 1024px) {

	.site-header--home .site-header__main-title {

        font-size: 3rem;

        word-spacing: -1.5px;

        padding:0;

     /*   background-color: rgba(51,51,51, 0.8); 

        padding:10px;

            height:80px;

        */

        line-height:normal;

            box-sizing: border-box;

        color:#fff;

        width:728px;

    text-shadow: 0 0 1em #0009;

    }

     .inventory__details {

         margin-top: 0.4rem;

		 

        }

    .site-header--home .site-header__area {

        padding-left:2em;

        padding-right:2em;

    }

     .home-slide__details {  

		width: 26%;

    	height:80px;

	/*	background-color: rgba(51,51,51, 0.8); */

	}

    .home-slide__detail {

        display:block;

    }

	.columna-esquerra {

		float:Left;

		width:33.3%

		

	}

	.cms__content--page-static .columna-esquerra .wp-caption {

		width:100%!important;

		float:none;

	}

	.columna-esquerra .alignleft {

		float:none;

	}

    .home-slide__description .wrapper {

      

    }

    .site-header__branding .sotstitol {

        font-size:1.3rem;

    }



}

@media screen and (min-width: 1280px) {



    .featured-conference__stamp {

        margin-top: 50px;

        padding-right: 14px;

        padding-left: 14px;

    }

    .site-header--home .site-header__main-title {

       

        width:758px;

    }.home-slide__description .wrapper {

   

}



}





 @media screen and (min-width: 80em) {

        .site-header--home .site-header__area {

          

	

        }

        .site-header--home .site-header__area {

     

        }

	.site-header--home .site-header__main-title {

        font-size: 4rem;

    }



    

}





















.site-header--home .site-header__branding {

    

    width: 90%;

	line-height:normal;

}



.home .site-header__branding .sotstitol{

	padding:10px;

    padding-left:0;

	background-color:rgba(255,255,255,1.00);

    

}



.home-slide__detail, .home-slide__detail a {

    color: rgba(255,255,255,1);

	display:inline-block;

    text-decoration:none;

}



@media screen and (min-width: 1024px) {

     .inventory__details {

         margin-top: 0.4rem;

        }

	.home .site-header__branding .sotstitol{

	padding:0px;

	background-color:rgba(255,255,255,0);

    color:#fff;

	max-width:100%;

        padding-bottom:1rem;

}

        .site-header--home .site-header__area {

    font-size: 1.14rem;

    margin-bottom: 1.5rem;

}



     .home-slide__details {  

		width: 26%;

    	height:80px;

	}

    .home-slide__detail {

        display:block;

        text-align:right;

    }



}

	



 @media screen and (min-width: 80em) {

        .site-header--home .site-header__area {

          

	

        }

        .site-header--home .site-header__area {

         

        }

	.site-header--home .site-header__main-title {

        font-size: 4rem;

    }

	

        .home-slide__details {

            bottom: 9px;

            height: 90.5px;

        }

    

}









@media screen and (min-width: 64em) {

	

	



        .site-header--home .site-header__branding::after {

            display:none;

        }

    



}



@media screen and (min-width: 64em) {

}





/* CAPA GRIS */





@media screen and (min-width: 64em) {

	

    .mosaic__map {

        min-height: 22.25rem;

        position: relative;

        height: 398px;

    }

    .mosaic__summary-item--details {

        margin: 4.125rem 0 0;

    }

    .mosaic__summary-item--stats {

        bottom: 30px;

    }

	 .mosaic__summary-item--image, .panel__cover {

		 margin:0;

		 text-align:center;

		 background: #000;

   /* background: -moz-linear-gradient(0deg, rgba(0,0,0,1) 0%, rgba(51,51,51,1) 100%);

    background: -webkit-linear-gradient(

0deg, #000f 0%, #333f 100%);

    background: linear-gradient(

0deg, #000f 0%, #333f 100%); */

		  position:relative;

	/*  margin:0;

		 overflow:hidden;

		

    min-height: 400px;

		 */

	}

	.mosaic__summary-item--image{

		display: flex;

		flex-direction: column;

		justify-content: center;

		height:auto;

	}

	

	.mosaic__summary-item--image img,.panel__cover img {

		max-height: 400px;

		margin:auto;

		display:block;cursor:pointer;

	

		

		/*

    object-fit: cover;

    width: 100%;

    height: 100%; */

		

}

	

	.mosaic__summary-item--image.full {

		 overflow: visible;

	}

	.mosaic__summary-item--image.full img {

		 overflow: visible;

		position:fixed;

		margin:auto;

		width:auto;

		height:auto;

		max-height:90vh;

		max-width:90vw;

		top:50%;

		left:50%;

		transform:translate(-50%,-50%);

		z-index:1001;

		box-sizing:border-box;

		filter: drop-shadow(0 0 5vw #000);

	}

	.mosaic__summary-item--image.full span {

		z-index:1000;

		background-color:#000e;

		display:block;

		left:0;

		top:0;

		position:fixed;

		width:100vw;

		height:100vh;

	}

	

	/*	

	-ms-transform: translate(-50%, -50%);

		-webkit-transform: translate(-50%, -50%); 

		transform: translate(-50%, -50%);

		position:absolute;

		left:50%;

		top:50%;  

	

		

*/

    .inventory__panel {

        height: 27.125rem;

    }

    .inventory__panel-contents {

        padding: 1.125rem 0 0;

    }

    .panel__mosaic-detail {

        font-size: 1.075rem;

    }

	.panel__mosaic-detail:nth-child(1) {

		font-size: 1.575rem;

		padding-top: 2rem;

	}

	.panel__mosaic-detail:nth-child(2) {

		padding-bottom: 2rem;

	}

    .panel__categories {

       width:100%;

    }

	.panel__action .button {

        font-size: 1.3rem;

        line-height: 4.25rem

    }

	.panel__description {

        width: 100%;

		margin-top: 0.5rem;

    }

}

.panel__description p{

	display:none;

}

.panel__summary {

    font-weight:900;

}

/* CONTENT */

.mosaic__description .cms__content a {

	color:rgba(0,0,0,1);

}

@media screen and (min-width: 64em) {

    .mosaic__description .cms__content {

		    max-width: 75%;

    margin: auto;

    margin-left: 25%;

        

        padding-right:4rem;

        box-sizing:border-box;

	}

	.mosaic__description .cms__content iframe {

		width:100%;

	}

	}

.wrapper--page-static {

   

    }





/* FOOTER */

.site-footer--external {

    background: rgba(0, 0, 0, 1);

    height: auto;

	

}



#footer {

    text-align: center;

    font-size: 14px;

    border-top:1px solid rgba(0,0,0,1.00);

}

#footer a {

	color: rgba(0,0,0,1.00);

}

.ccomons, .brand-peu {

	

	padding:10px;

	padding-top:0px;

	padding-bottom:0px;



}

.ccomons {

	background-color: #f9f9f9;

	margin:0px;

	padding:4px;



}

#footer .brand-peu {

	background-color: rgba(0,0,0,1.00);

	margin:0px;

	padding:10px;

	color:rgba(255,255,255,1.00);

}

#footer .brand-peu a{

	color:rgba(255,255,255,1.00);

}

#colophon {

    position: relative;

    width: 90%;

    margin: auto;

    padding-top: 0rem;

   

    margin-left: auto;

    margin-right: auto;

    padding-bottom: 0rem;

        max-width: 100rem;

}

@media(min-width:64rem){
	
	#colophon {
	padding-top:4rem;
	}
}

.creative-commons {

    clear:both;

}

    @media screen and (min-width: 1024px) {

        .site-footer__aside .wrapper {

			

        }

    }





    @media screen and (min-width: 64em) {

        .entry-title{

            font-size:2.25rem;

			font-weight:400;

        }

		 .site-footer__sponsors {

      

            border-right:1px dotted;

        }

		 .site-footer__sponsors, .site-footer__comite {

           width:100%;

           padding-top:20px;

        }

		   .site-footer__collaborators img, .site-footer__sponsors img {

			   max-height:300px;

		}

		   .site-footer__collaborators {

            width: 100%;

            border-bottom:1px dotted;

        }

    }

.site-content {

	/* max-width: 64rem; */

	margin: 0 auto;

	padding-bottom:40px;

}

.site-content p, .site-content li {

	

}

@media screen and (min-width: 48em) {

    .site-footer--external {

        height: auto;

    }



}

.cms__content {

	line-height:150%;

}

.participate__item--description .cms__content{

	padding-right: 30px;

}

.participate__item--description .cms__content h3{

	font-size:18px;



}

@media screen and (min-width: 64em) {

    .site-footer__collaborators .site-footer__brand-list {

          margin-bottom: 1rem;

    }

}


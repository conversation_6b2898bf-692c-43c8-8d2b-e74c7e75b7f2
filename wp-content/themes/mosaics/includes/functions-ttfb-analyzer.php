<?php
/**
 * Analitzador de TTFB (Time To First Byte) per al tema Mosaics
 * 
 * Aquest fitxer analitza el rendiment del servidor i identifica
 * els colls d'ampolla que causen un TTFB alt de 6,111ms
 * 
 * @package Mosaics
 * @version 1.0.0
 */

// Evitar accés directe
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ANALITZADOR DE TTFB
 */

// Iniciar mesura de temps al començament de la càrrega
if (!defined('MOSAICS_START_TIME')) {
    define('MOSAICS_START_TIME', microtime(true));
}

// Mesures de temps per diferents fases
global $mosaics_timing;
$mosaics_timing = array(
    'start' => MOSAICS_START_TIME,
    'checkpoints' => array()
);

/**
 * Afegir checkpoint de temps
 */
function mosaics_add_timing_checkpoint($name, $description = '') {
    global $mosaics_timing;
    
    $current_time = microtime(true);
    $elapsed = ($current_time - MOSAICS_START_TIME) * 1000; // Convertir a ms
    
    $mosaics_timing['checkpoints'][] = array(
        'name' => $name,
        'description' => $description,
        'time' => $current_time,
        'elapsed' => $elapsed,
        'memory' => memory_get_usage(true),
        'peak_memory' => memory_get_peak_usage(true)
    );
    
    // Log en mode debug
    if (defined('WP_DEBUG') && WP_DEBUG) {
        error_log(sprintf('[TIMING] %s: %.2fms (Memory: %s)', $name, $elapsed, size_format(memory_get_usage(true))));
    }
}

/**
 * Analitzar consultes de base de dades
 */
function mosaics_analyze_database_queries() {
    global $wpdb;
    
    if (!defined('SAVEQUERIES') || !SAVEQUERIES) {
        return array('error' => 'SAVEQUERIES no està activat');
    }
    
    $total_time = 0;
    $slow_queries = array();
    $query_types = array();
    
    foreach ($wpdb->queries as $query) {
        $time = $query[1] * 1000; // Convertir a ms
        $total_time += $time;
        
        // Detectar consultes lentes (>100ms)
        if ($time > 100) {
            $slow_queries[] = array(
                'query' => $query[0],
                'time' => $time,
                'stack' => $query[2]
            );
        }
        
        // Categoritzar tipus de consulta
        $type = 'OTHER';
        if (preg_match('/^SELECT/i', $query[0])) $type = 'SELECT';
        elseif (preg_match('/^INSERT/i', $query[0])) $type = 'INSERT';
        elseif (preg_match('/^UPDATE/i', $query[0])) $type = 'UPDATE';
        elseif (preg_match('/^DELETE/i', $query[0])) $type = 'DELETE';
        
        $query_types[$type] = ($query_types[$type] ?? 0) + 1;
    }
    
   $num_queries = count($wpdb->queries);

    return array(
        'total_queries' => $num_queries,
        'total_time'    => $total_time,
        'average_time'  => $num_queries > 0 ? ($total_time / $num_queries) : 0,
        'slow_queries'  => $slow_queries,
        'query_types'   => $query_types
    );
}

/**
 * Analitzar plugins actius
 */
function mosaics_analyze_active_plugins() {
    $active_plugins = get_option('active_plugins', array());
    $plugin_data = array();
    
    foreach ($active_plugins as $plugin) {
        $plugin_file = WP_PLUGIN_DIR . '/' . $plugin;
        if (file_exists($plugin_file)) {
            $plugin_info = get_plugin_data($plugin_file);
            $plugin_data[] = array(
                'name' => $plugin_info['Name'],
                'version' => $plugin_info['Version'],
                'file' => $plugin,
                'size' => filesize($plugin_file)
            );
        }
    }
    
    return $plugin_data;
}

/**
 * Analitzar ús de memòria
 */
function mosaics_analyze_memory_usage() {
    return array(
        'current' => memory_get_usage(true),
        'peak' => memory_get_peak_usage(true),
        'limit' => ini_get('memory_limit'),
        'percentage' => (memory_get_peak_usage(true) / wp_convert_hr_to_bytes(ini_get('memory_limit'))) * 100
    );
}

/**
 * Analitzar configuració del servidor
 */
function mosaics_analyze_server_config() {
    return array(
        'php_version' => PHP_VERSION,
        'max_execution_time' => ini_get('max_execution_time'),
        'memory_limit' => ini_get('memory_limit'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'opcache_enabled' => function_exists('opcache_get_status') && opcache_get_status(),
        'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown'
    );
}

/**
 * Generar informe complet de TTFB
 */
function mosaics_generate_ttfb_report() {
    global $mosaics_timing;
    
    $report = array(
        'timestamp' => current_time('mysql'),
        'total_time' => (microtime(true) - MOSAICS_START_TIME) * 1000,
        'checkpoints' => $mosaics_timing['checkpoints'],
        'database' => mosaics_analyze_database_queries(),
        'memory' => mosaics_analyze_memory_usage(),
        'plugins' => mosaics_analyze_active_plugins(),
        'server' => mosaics_analyze_server_config()
    );
    
    return $report;
}

/**
 * Mostrar informe de TTFB en el footer (només en debug)
 */
function mosaics_display_ttfb_report() {
    if (!defined('WP_DEBUG') || !WP_DEBUG || is_admin()) {
        return;
    }
    
    $report = mosaics_generate_ttfb_report();
    
    echo "\n<!-- TTFB ANALYSIS REPORT -->\n";
    echo "<script>\n";
    echo "console.group('🔍 TTFB Analysis Report');\n";
    echo "console.log('Total generation time: " . number_format($report['total_time'], 2) . "ms');\n";
    
    // Checkpoints
    if (!empty($report['checkpoints'])) {
        echo "console.group('⏱️ Timing Checkpoints');\n";
        foreach ($report['checkpoints'] as $checkpoint) {
            echo "console.log('" . esc_js($checkpoint['name']) . ": " . number_format($checkpoint['elapsed'], 2) . "ms');\n";
        }
        echo "console.groupEnd();\n";
    }
    
    // Database
    if (isset($report['database']['total_queries'])) {
        echo "console.group('🗄️ Database Analysis');\n";
        echo "console.log('Total queries: " . $report['database']['total_queries'] . "');\n";
        echo "console.log('Total DB time: " . number_format($report['database']['total_time'], 2) . "ms');\n";
        echo "console.log('Average query time: " . number_format($report['database']['average_time'], 2) . "ms');\n";
        
        if (!empty($report['database']['slow_queries'])) {
            echo "console.warn('Slow queries detected: " . count($report['database']['slow_queries']) . "');\n";
            foreach ($report['database']['slow_queries'] as $i => $query) {
                if ($i < 5) { // Només mostrar les 5 primeres
                    echo "console.warn('Slow query " . ($i + 1) . ": " . number_format($query['time'], 2) . "ms');\n";
                }
            }
        }
        echo "console.groupEnd();\n";
    }
    
    // Memory
    echo "console.group('💾 Memory Usage');\n";
    echo "console.log('Peak memory: " . size_format($report['memory']['peak']) . "');\n";
    echo "console.log('Memory percentage: " . number_format($report['memory']['percentage'], 1) . "%');\n";
    echo "console.groupEnd();\n";
    
    // Plugins
    echo "console.group('🔌 Active Plugins');\n";
    echo "console.log('Total plugins: " . count($report['plugins']) . "');\n";
    echo "console.groupEnd();\n";
    
    echo "console.groupEnd();\n";
    echo "</script>\n";
    echo "<!-- END TTFB ANALYSIS REPORT -->\n";
}
add_action('wp_footer', 'mosaics_display_ttfb_report', 999);

/**
 * CHECKPOINTS DE TIMING AUTOMÀTICS
 */

// Checkpoint: Després de carregar WordPress
add_action('wp_loaded', function() {
    mosaics_add_timing_checkpoint('wp_loaded', 'WordPress core loaded');
});

// Checkpoint: Després de carregar el tema
add_action('after_setup_theme', function() {
    mosaics_add_timing_checkpoint('theme_loaded', 'Theme loaded');
});

// Checkpoint: Després de carregar plugins
add_action('plugins_loaded', function() {
    mosaics_add_timing_checkpoint('plugins_loaded', 'All plugins loaded');
});

// Checkpoint: Abans de la consulta principal
add_action('pre_get_posts', function() {
    mosaics_add_timing_checkpoint('pre_query', 'Before main query');
});

// Checkpoint: Després de la consulta principal
add_action('wp', function() {
    mosaics_add_timing_checkpoint('post_query', 'After main query');
});

// Checkpoint: Abans de renderitzar el template
add_action('template_redirect', function() {
    mosaics_add_timing_checkpoint('template_redirect', 'Template redirect');
});

/**
 * FUNCIONS D'OPTIMITZACIÓ ESPECÍFIQUES
 */

// Desactivar funcionalitats innecessàries per millorar TTFB
function mosaics_optimize_ttfb() {
    // Desactivar pingbacks/trackbacks
    add_filter('xmlrpc_enabled', '__return_false');
    
    // Desactivar embeds de WordPress
    remove_action('wp_head', 'wp_oembed_add_discovery_links');
    remove_action('wp_head', 'wp_oembed_add_host_js');
    
    // Desactivar feeds si no es necessiten
    remove_action('wp_head', 'feed_links', 2);
    remove_action('wp_head', 'feed_links_extra', 3);
    
    // Optimitzar heartbeat
    add_filter('heartbeat_settings', function($settings) {
        $settings['interval'] = 60; // Reduir freqüència
        return $settings;
    });
    
    // Limitar revisions
    if (!defined('WP_POST_REVISIONS')) {
        define('WP_POST_REVISIONS', 3);
    }
}
add_action('init', 'mosaics_optimize_ttfb');

/**
 * ALERTES AUTOMÀTIQUES PER RENDIMENT LENT
 */
function mosaics_check_performance_alerts() {
    if (!defined('WP_DEBUG') || !WP_DEBUG) {
        return;
    }
    
    $total_time = (microtime(true) - MOSAICS_START_TIME) * 1000;
    
    // Alerta si el temps total és > 3 segons
    if ($total_time > 3000) {
        error_log('[PERFORMANCE ALERT] Page generation time: ' . number_format($total_time, 2) . 'ms');
        
        // Analitzar causes possibles
        $db_analysis = mosaics_analyze_database_queries();
        if (isset($db_analysis['total_time']) && $db_analysis['total_time'] > 1000) {
            error_log('[PERFORMANCE ALERT] Database queries taking too long: ' . number_format($db_analysis['total_time'], 2) . 'ms');
        }
        
        $memory = mosaics_analyze_memory_usage();
        if ($memory['percentage'] > 80) {
            error_log('[PERFORMANCE ALERT] High memory usage: ' . number_format($memory['percentage'], 1) . '%');
        }
    }
}
add_action('wp_footer', 'mosaics_check_performance_alerts', 1000);

/**
 * ENDPOINT AJAX PER OBTENIR INFORME DE RENDIMENT
 */
function mosaics_ajax_performance_report() {
    // Verificar nonce
    if (!wp_verify_nonce($_GET['nonce'], 'performance_report')) {
        wp_die('Invalid nonce');
    }
    
    $report = mosaics_generate_ttfb_report();
    
    wp_send_json_success($report);
}
add_action('wp_ajax_get_performance_report', 'mosaics_ajax_performance_report');
add_action('wp_ajax_nopriv_get_performance_report', 'mosaics_ajax_performance_report');

/**
 * FUNCIÓ PER ACTIVAR LOGGING DETALLAT
 */
function mosaics_enable_detailed_logging() {
    // Activar logging de consultes (forçar si no està definit)
    if (!defined('SAVEQUERIES')) {
        define('SAVEQUERIES', true);
    }

    // Si ja està definit però és false, mostrar avís
    if (defined('SAVEQUERIES') && !SAVEQUERIES) {
        error_log('[MOSAICS] SAVEQUERIES està definit com false. Afegir define("SAVEQUERIES", true); a wp-config.php per anàlisi completa.');
    }
    
    // Activar logging d'errors PHP
    ini_set('log_errors', 1);
    ini_set('error_log', WP_CONTENT_DIR . '/debug.log');
    
    // Activar profiling si xdebug està disponible
    if (function_exists('xdebug_start_trace')) {
        xdebug_start_trace(WP_CONTENT_DIR . '/xdebug_trace');
    }
}

// Activar logging detallat en mode debug
if (defined('WP_DEBUG') && WP_DEBUG) {
    mosaics_enable_detailed_logging();
}
